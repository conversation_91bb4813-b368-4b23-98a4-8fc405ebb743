<template>
	<div class="flex items-center">
		<div class="flex items-center px-[1rem] mr-[.8rem] rounded-[.2rem] opacity-80" :class="{ 'recom-bg': authType === 'free' && !props.isHome }">
			<span :class="(authType === 'free' && !props.isHome ? 'icon-end-time ' : 'icon-end-time-free ') + 'icon-' + (authType === 'free' ? 'un' : '') + 'paid-' + (authType === 'free' ? recommendAuth : authType)" @click="openProductPayView">
				<template v-if="authType !== 'free'">
					<span class="leading-[1.6rem]">
						到期时间：
						<span
							class="font-bold !mr-2"
							:class="{
								'text-danger': authRemainingDays < 7,
								'text-[#fc6d26]': authRemainingDays >= 7,
							}">
							{{ authExpirationTime }}
						</span>
					</span>
					<bt-link v-if="authRemainingDays !== 0 && authExpirationTime !== '永久授权'" class="!align-top" style="font-weight: bold; color: #20a53a"> 续费 </bt-link>
				</template>
				<span v-show="authType === 'free' && !props.isHome" class="ml-[.4rem] font-bold text-[#ef9f00]">安全、高效、让您更安心</span>
			</span>
		</div>
		<span v-show="authType === 'free' && props.isHome" class="mr-1rem ml-[-1.5rem]" @click="openProductPayView">免费版</span>
		<button v-show="authType === 'free' && !props.isHome" class="recom-btn" @click="openProductPayView">立即体验</button>
	</div>
</template>

<script setup lang="ts">
import { productPaymentDialog } from '@/public'
import { useGlobalStore } from '@store/global'

const { authRemainingDays, authExpirationTime, payment, aliyunEcsLtd, forceLtd } = useGlobalStore()
const { recommendAuth, authType } = toRefs(payment.value)

const props = withDefaults(defineProps<{ disablePro?: boolean; isHome?: boolean }>(), {
	disablePro: false,
	isHome: false,
})

const showClose = computed(() => {
	if (aliyunEcsLtd.value && payment.value.authType !== 'ltd') return false
	if (forceLtd.value && payment.value.authType !== 'ltd') return false
	return true
})

/**
 * @description 打开产品支付弹窗
 */
const openProductPayView = () => {
	const config = { disablePro: true }
	// if (payment.value.authType === 'ltd') config.disablePro = false
	// if ((props.isHome && authType.value !== 'ltd' && authType.value !== 'free') || (!props.isHome && authType.value === 'pro')) config.disablePro = false
	if (authType.value === 'pro') config.disablePro = false // 如果当前是专业版，不禁用专业版
	productPaymentDialog({
		disablePro: config.disablePro,
		sourceId: 27,
		showClose: showClose.value,
	})
}
</script>

<style lang="css" scoped>
.recom-bg {
	@apply bg-[#FEF7EB] rounded-[.4rem];
}

.recom-btn {
	@apply bg-[#ef9f00] text-white w-[6.2rem] h-[2.4rem] mr-[16px] rounded-[.2rem] px-[.6rem] py-[.2rem] border-none opacity-80;
}

.recom-btn:hover {
	@apply bg-[#E7AA31];
}

.icon-end-time {
	@apply flex items-center h-[2.2rem] pl-28 bg-no-repeat bg-left cursor-pointer leading-[2.2rem] relative;
	background-size: 6.4rem;
}

.icon-end-time::after {
	@apply content-[''] inline-block w-[0] h-[0] top-[50%] -right-[1.8rem] -mt-[.5rem] absolute;
	border-top: 6px solid transparent;
	border-bottom: 6px solid transparent;
	border-left: 8px solid #fef7eb;
}

.icon-end-time-free {
	@apply flex items-center h-[2.2rem] pl-28 bg-no-repeat bg-left cursor-pointer leading-[2.2rem] relative;
	background-size: 6.4rem;
	background-position: 0 2px;
}

.icon {
	@apply h-[2.2rem] w-[6.4rem];
}

.icon-paid-pro {
	background-image: url('/static/images/vip/vip-pro.svg');
}

.icon-unpaid-pro {
	background-image: url('/static/images/vip/vip-pro-not.svg');
}

.icon-paid-ltd {
	background-image: url('/static/images/vip/vip-ltd.svg');
}

.icon-unpaid-ltd {
	background-image: url('/static/images/vip/vip-ltd-not.svg');
}
</style>
