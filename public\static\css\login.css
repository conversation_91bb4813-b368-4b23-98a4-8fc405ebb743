@font-face {
  font-family: "svgtofont";
  src: url('../font/svgtofont.eot?t=1731579762035'); /* IE9*/
  src: url('../font/svgtofont.eot?t=1731579762035#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("../font/svgtofont.woff2?t=1731579762035") format("woff2"),
  url("../font/svgtofont.woff?t=1731579762035") format("woff"),
  url('../font/svgtofont.ttf?t=1731579762035') format('truetype') /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
}



[class^="svgtofont-"], [class*=" svgtofont-"] {
  font-family: 'svgtofont' !important;font-size: 14px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


.svgtofont-el-question-filled:before { content: "\ea3b"; color: rgb(255, 136, 0);}

* {
	box-sizing: border-box;
}
html,
body,
#app {
	font-size: 10px;
	position: relative;
	width: 100%;
	height: 100%;
}
body {
	margin: 0;
	padding: 0;
	background-color: #fff;
	line-height: 1.4;
	font-size: 1.2rem;
	font-family:
		PingFang SC,
		HarmonyOS_Medium,
		Helvetica Neue,
		Microsoft YaHei,
		sans-serif !important;
	color: #555;
	scrollbar-width: thin;
}

.loading-icon.svgtofont-el-question-filled:before {
	/* content: "\ea50"; */
	font-size: 20px;
	color: #e6a23c;
}

.loading-icon {
	display: flex;
	align-items: center;
	margin-left: 12px;
	font-size:20px;
	/* color:#e6a23c */
}

.loading-icon .el-message__icon {
	font-size: 30px;
	width: 30px;
	height: 30px;
}

.loading-icon.svgtofont-el-circle-check-filled:before {
	color: #67c23a;
	font-size: 30px;
	content: '\ea19';
}

.loading-icon.svgtofont-el-circle-close-filled:before {
	color: #f56c6c;
	font-size: 30px;
	content: "\ea1b";
}

.\!hidden {
	display: none !important;
}
.\!mt-0 {
	margin-top: 0 !important;
}
.w-full,
.w-\[100\%\] {
	width: 100%;
}

.\!w-\[230px\] {
	width: 230px !important;
}
.w-\[140px\] {
	width: 140px;
}
.h-\[140px\] {
	height: 140px;
}
.h-\[40px\] {
	height: 40px;
}

.leading-8 {
	line-height: 20px;
}

.p-\[\8px\] {
	padding: 8px;
}

.mb-\[\.8rem\] {
	margin-bottom: 0.8rem;
}

.font-bold {
	font-weight: 700;
}

.text-danger {
	color: #ef0808;
}
.w-\[40rem\] {
	width: 40rem;
}

.mr-\[15px\] {
	margin-right: 15px;
}

.flex {
	display: flex;
}

/* 登录背景 */
.login-bg {
	background-color: #444;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 登录主体内容 */
.login-main {
	background-color: #444;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	position: relative;
}

/* 切换登录方式 */
.login-main .cut-login-type {
	cursor: pointer;
	height: 60px;
	position: absolute;
	z-index: 999;
	right: 8px;
	top: 120px;
	width: 60px;
	background-image: url('/static/images/other/login_cut.png');
	background-position: 0 0;
	background-size: 120px;
}

.login-main .cut-login-type:hover {
	background-position: -60px 0;
}

.login-main .cut-login-type.scan {
	background-position: 0rem -60px;
}

.login-main .cut-login-type .tips-text {
	font-size: 12px;
}

.login-main .cut-login-type.scan:hover {
	background-position: -60px -60px;
}

.login-main .cut-login-type .tips {
	background-color: rgba(223, 240, 216, 1);
	border-radius: 0.4rem;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 28px;
	position: absolute;
	top: 16px;
	right: 56px;
	text-align: center;
	color: rgba(32, 165, 58, 1);
	width: 112px;
	animation: rock 1s 0s ease-in-out infinite;
}

.login-main .cut-login-type .tips > span:nth-child(1) {
	background-repeat: no-repeat;
	display: inline-block;
	height: 14px;
	margin-right: 4px;
	position: relative;
	top: 0.5px;
	width: 14px;
	background-size: 14px;
}

.login-main .cut-login-type .tips > span.account {
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAgCAYAAACYTcH3AAAAAXNSR0IArs4c6QAAAdFJREFUWEdjZIACuSUW1kyMTKUMDAyODAwMfDBxLHTDg+hjjXjkGUSnOvBwC/7czPCf0QGPugcMDAy7HkQfS4epYQQxoA45gs8CJLkHD6KPKeJTK7/MKovxP8NUIs2Dew7sGIWlVhMYGBjyoZqvMjD+f43LIKZ/DL33Yo5vIWSR/BKrYkam/z441SGF2h/W/zJPwo4/hThmmeV+WJD++8+s/Sjm8DVCllEqr7jUMvQ/A+MqsDn//jk+iD1xAMMxD6KPgcVoDRQWWzgwMDHtH3UMekiPhgyutDdUQsa6n+H//4L//xnuPIw5pkrrnAQyX3aBlTIzK8MdEJuJmUn9XsSRW/BsrLDMMpKB8f/xB5EnQMU0XQA4qhgYGEBlDIimS5lCrM8Gl2MUl1i5/Wf6X0ms62mi7j/T6wfRR8MYFZZaXWFgYNCmiSWkGZoFcsx8BgaGBNL0UV316/+MDBGDK81Q3Y8UGAgPGbmlNsb/WP++ADVyKDCPJK1yS2y1WNn+/Lobdhxc+MFaevUMDAwNDAwMVx9EH9MhyUQyFSuvslT5+5vxNrhtBW3QjTauQKExVGptRIN8tA082jtAKgaISsAD34kbTN3bQdXxB0XlYBgSAQBVv1HZQYwH1wAAAABJRU5ErkJggg==);
}

.login-main .cut-login-type .tips > span.scan {
	height: 1.6rem;
	top: -0.5px;
	width: 1.6rem;
	background-size: 1.6rem;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADAElEQVRYR8VWS1LbQBTs51Sogo3NCTKcAC+xN5gTxCyDSGFOgHOCJCeIOQGmgpIl5gSYjcTSOQGTE8TeOJSp6KVmRvJHGlljXAWzorDmdc/rfh/CKx96ZXw4ERDf9xoolfbBXAFQ1aSJZn8DAzAP48cMQDQEcV8ehXdFD8wlEIOegNEEQYGlDv8GION/CoDeZT/BEIQeouhSfrzv28hkCAi/XgX4GiBhLmigHgh9MEnpBYNlr9L3iQUYDQDNGTGWAB2m71sI1PoAqXRfgqhTBFiUYk2IuQ2iE4DvpBcqYtOTS0B6gZM/iggkvwu/zisQQFV6oUV3V7jsdysQsDN9PrS5Kfya8tCuPA62CyRYn4D4uSfw+DiUp4OkNA0B0H5aWlsVWLVyzYAxHW5BuJNe0Jx54AUIzIFXQHwoj8LeixFYAEd0Kr377nzW1pZAXFQr85ouBp+mvQIL+NSEz/WA8Os9MPZBOEg3pqKXL0qQLW97I0qVi/hRa4LpGqx7+5SEK7jJwEqNKFsuwt9rAaWLhIQZE9rtuWlPyeTaCevKue8xGW+nNV8goaK7gl9UK9jY+gPgZr40TYjUEX79C4DPiKID2widktD3sm639Yt4tN8C+Cq9QMVfNoySVPMneRx2cgOqZSBnxmcedVVT0/CbjXA2A6qNRqUHW7pcu6Elq0bWUrQjP9wnS4z+zDpyhV+TYCrjabyTV/uuZFT/wNutBxCPpBfGS84SCeKSMT4AutILTl3BrHJd1bp6GWG7pPk74VV9AMKuq9Gs4LPS/SWPA7PMpk4+Ae0FGgBUBrgtvfB8lUyIqfF4hBJX09onsZauXfGCquZ4Gcx9PP09LPKE0XzzGkQNgEcANZbtlYV7nw64sdkzi6puxR1MxueZJmWazRkY7XiNv8Fk3CoiXEggSZVpQNQx2cAQ4C7esJHlH50B1DLA6tVKssVxnCefMwFdHeaVbQVgvDF/dLpVdjpFr17aCV2NFk/Ilukm3J3fflxj5DaiVQKs++1KEqwLZrv/HyI/ujC8s0y6AAAAAElFTkSuQmCC);
}

.login-main .cut-login-type .tips:before {
	margin-left: 8px;
	position: absolute;
	top: 8px;
	right: -12px;
	content: '';
	border: 6px solid #dff0d8;
	border-color: transparent transparent transparent #dff0d8;
}

.login-main .cut-login-type.account-login {
	background-position: 0 0;
}

.login-main .cut-login-type.account-login:hover {
	background-position: -40px 0;
}

.login-main .cut-login-type.scan-code-login {
	background-position: 0 -60px;
}

.login-main .cut-login-type.scan-code-login:hover {
	background-position: -60px -60px;
}
.text-small {
	font-size: 12px;
}
.text-middle {
	font-size: 14px;
}

/* 链接样式 */

.bt-link {
	font-size: 12px;
	color: #20a53a;
	text-decoration: none;
}

.bt-link:hover {
	color: #1e7e34;
}

/* 登录图标 */
.login-svg {
	text-align: center;
	margin-bottom: 1.25rem; /* mb-5 corresponds to 1.25rem in Tailwind */
	width: 100%;
}

.login-svg svg {
	height: 88px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	fill: currentColor;
	vertical-align: -1.5px;
	overflow: hidden;
}

.login-account,
.login-scan {
	width: 440px;
	position: relative;
	padding: 40px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: #fff;
	border-radius: 4px;
}

/* 登录表单 */
.login-form {
	display: flex;
	flex-direction: column;
	position: relative;
	text-align: center;
	margin-top: 5px;
}

.login-form.google-verify-view {
	margin-top: 0;
}

.login-title {
	margin: 25px;
	font-size: 26px;
	color: #000;
	display: flex;
	justify-content: center;
}

.title-text{
	width:100%;
	text-overflow: ellipsis;
	overflow: hidden;
	text-align: center;
	white-space: nowrap;
}

.login-form-item {
	width: 100%;
}

.form-group {
	position: relative;
	padding-bottom: 22px;
	display: flex;
	align-items: center;
}

.form-group .error-tips {
	font-size: 12px;
	display: block;
	position: absolute;
	bottom: 6px;
	left: 2px;
}

.form-group.error .login-form-input {
	border-color: #ef0808 !important;
}

.form-group.error .error-tips {
	color: #ef0808;
}

.login-form-input {
	width: 100%;
	background-color: #fff;
	background-image: none;
	border-radius: 4px;
	border: 1px solid #dcdfe6;
	box-sizing: border-box;
	color: #666;
	display: inline-block;
	font-size: inherit;
	height: 40px;
	line-height: 40px;
	font-size: 14px;
	outline: none;
	padding: 0 15px;
	transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.login-form-input:hover {
	border-color: #c2c2c2;
}

.login-form-input:focus {
	border-color: #20a53a !important;
}

.login-form-item .cut-login-code {
	width: 115px;
	height: 40px;
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	cursor: pointer;
	display: flex;
	align-items: center;
}
.login-form-item .cut-login-code img {
	border-radius: 4px;
	font-size: 12px;
}

.login-form-button {
	width: 100%;
	color: #fff;
	background-color: #20a53a;
	border-color: #20a53a;
	border-radius: 2px;
	cursor: pointer;
	display: inline-block;
	line-height: 1;
	white-space: nowrap;
	cursor: pointer;
	text-align: center;
	box-sizing: border-box;
	outline: none;
	margin: 0;
	transition: 0.1s;
	font-weight: 500;
	padding: 12px 20px;
	font-size: 14px;
	border: none;
}

.login-form-button:hover,
.login-form-button:focus {
	background: #4db761;
	border-color: #4db761;
	color: #fff;
}

.login-form-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.login-form-footer .cdn-node {
	background-color: rgba(239, 239, 239, 1);
	border-color: rgba(220, 223, 230, 1);
	border-top-left-radius: 2.5px;
	border-bottom-left-radius: 2.5px;
	border-width: 1px;
	border-right-width: 0px;
	display: flex;
	align-items: center;
	height: 28px;
	padding-left: 8px;
	padding-right: 8px;
	font-size: 12px;
	color: rgba(153, 153, 153, 1);
}

.login-form-footer .form-select-module {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}

/* 谷歌认证 */

.login-google-verify {
	width: 360px;
	background-color: rgba(255, 255, 255, 1);
	border-radius: 4px;
	padding: 60px 30px 40px 30px;
	position: fixed;
	top: 50%;
	margin-top: -140px;
	z-index: 999;
	box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);
}

.login-form li {
	text-align: left;
	font-size: 12px;
	line-height: 1.8;
}

.google-verify-shade {
	background-color: rgba(0, 0, 0, 1);
	height: 100%;
	opacity: 0.3;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 998;
}

.close-popup-btn {
	background-position: center;
	background-repeat: no-repeat;
	background-size: contain;
	cursor: pointer;
	height: 30px;
	position: absolute;
	width: 30px;
	transform-origin: center;
	background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTAiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxjaXJjbGUgc3R5bGU9ImZpbGw6I2ZmZiIgY3g9IjI1IiBjeT0iMjUiIHI9IjI1Ii8+PGNpcmNsZSBzdHlsZT0iZmlsbDojNzg3ODc4IiBjeD0iMjUiIGN5PSIyNSIgcj0iMjAuNSIvPjxwYXRoIHN0eWxlPSJmaWxsOiNmZmYiIGQ9Im0zMi4wNzEgMzQuODk5LTE2Ljk3LTE2Ljk3YTIgMiAwIDEgMSAyLjgyOC0yLjgyOEwzNC45IDMyLjA3MmEyIDIgMCAxIDEtMi44MjkgMi44Mjd6Ii8+PHBhdGggc3R5bGU9ImZpbGw6I2ZmZiIgZD0ibTM0Ljg5OSAxNy45MjktMTYuOTcgMTYuOTdhMiAyIDAgMSAxLTIuODI4LTIuODI4TDMyLjA3MiAxNS4xYTIgMiAwIDEgMSAyLjgyNyAyLjgyOXoiLz48L3N2Zz4=);
	background-size: contain contain;
	background-position: center;
	background-repeat: no-repeat;
	transition: transform 0.4s ease-in-out;
	right: -15px;
	top: -15px;
}

.close-popup-btn:hover {
	transform: rotate(180deg);
	background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTAiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxjaXJjbGUgc3R5bGU9ImZpbGw6I2ZmZiIgY3g9IjI1IiBjeT0iMjUiIHI9IjI1Ii8+PGNpcmNsZSBzdHlsZT0iZmlsbDojZjEyOTMwIiBjeD0iMjUiIGN5PSIyNSIgcj0iMjAuNSIvPjxwYXRoIHN0eWxlPSJmaWxsOiNmZmYiIGQ9Im0zMi4wNzEgMzQuODk5LTE2Ljk3LTE2Ljk3YTIgMiAwIDEgMSAyLjgyOC0yLjgyOEwzNC45IDMyLjA3MmEyIDIgMCAxIDEtMi44MjkgMi44Mjd6Ii8+PHBhdGggc3R5bGU9ImZpbGw6I2ZmZiIgZD0ibTM0Ljg5OSAxNy45MjktMTYuOTcgMTYuOTdhMiAyIDAgMSAxLTIuODI4LTIuODI4TDMyLjA3MiAxNS4xYTIgMiAwIDEgMSAyLjgyNyAyLjgyOXoiLz48L3N2Zz4=);
}

/* 登录扫描 */
.login-scan-view {
	display: flex;
	flex: 1;
	position: relative;
	align-items: center;
	flex-direction: column;
}

.login-scan-success {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.login-scan-error {
	display: flex;
	flex-direction: column;
	padding: 40px 0;
}

/* 下拉框 */

.form-select-module.small {
	height: 28px;
	line-height: 28px;
	font-size: 12px;
}

.form-select-module.small .select-display {
	padding: 0 30px 0 10px;
}

.form-select-module.small .select-display:after {
	right: 10px;
	top: 12px;
}

.form-select-module.active.small .select-list {
	top: 32px;
}

.form-select-module.small .select-list .select-item {
	height: 28px;
	line-height: 28px;
}

.form-select-module {
	position: relative;
	text-align: left;
	height: 40px;
	line-height: 40px;
	border-radius: 4px; /* assuming default corresponds to 4px */
	font-size: 14px;
	border: 1px solid #dcdfe6;
	cursor: pointer;
}

.form-select-module .select-display {
	padding: 0 40px 0 15px;
}
.form-select-module .select-display:hover {
	border-color: #c2c2c2;
}

.form-select-module .select-display:after {
	content: '';
	position: absolute;
	top: 50%;
	right: 15px;
	margin-top: -5px;
	width: 8px;
	height: 8px;
	border-bottom: 1.5px solid #c2c2c2;
	border-right: 1.5px solid #c2c2c2;
	transform: rotate(45deg);
	transition: transform 0.3s ease-in-out;
}
.form-select-module.small .select-display:after {
	width: 6px;
	height: 6px;
}

.form-select-module .select-list {
	display: none;
	list-style-type: none;
	box-sizing: border-box;
	margin: 0;
	padding-top: 6px;
	padding-bottom: 6px;
	position: absolute;
	top: 40px;
	left: 0;
	width: 100%;
	background-color: #fff;
	border-color: #dcdfe6;
	border-width: 1px;
	border-radius: 2px; /* assuming default corresponds to 4px */
	z-index: 100;
	max-height: 200px;
	overflow: auto;
}

@media screen and (max-height: 950px) {
	.login-cdn-accelerate.form-select-module .select-list {
		top: -205px !important;
	}
}

/* 激活选中 */
.form-select-module.active .select-display:after {
	transform: rotate(225deg);
}

.form-select-module.active .select-display {
	border-color: #20a53a;
}

.form-select-module.active .select-list {
	width: inherit;
	display: block;
	position: absolute;
	top: 50px;
	z-index: 90;
	border-style: solid;
	border-width: 1px;
	border-color: #e4e7ed;
	border-radius: 4px;
	background-color: white;
	box-shadow:
		0 1px 3px 0 rgba(0, 0, 0, 0.1),
		0 1px 2px 0 rgba(0, 0, 0, 0.06); /* assuming shadow-md corresponds to these values */
	box-sizing: border-box;
}

/* 下拉角标 */
.form-select-module .select-list::after {
	content: '';
	position: absolute;
	top: -6px;
	left: 50%;
	margin-left: -6px;
	border: 6px solid transparent;
	border-bottom-color: #fff;
}

/* 下拉选项 */
.form-select-module .select-list .select-item {
	font-size: 12px;
	padding: 0 20px;
	position: relative;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: #666;
	height: 34px;
	line-height: 34px;
	box-sizing: border-box;
	cursor: pointer;
	text-align: left;
}
.form-select-module .select-list .select-item.disabled {
	color: #c2c2c2;
	cursor: not-allowed;
}

.form-select-module .select-list .select-item.active {
	color: #20a53a;
	font-weight: 700;
}

.form-select-module .select-list .select-item:hover {
	background-color: #f5f7fa;
}

/* 扫码 */

.login-scan-canvas {
	width: 140px;
	height: 140px;
	margin-bottom: 10px;
}

.login-scan-tips {
	font-size: 16px;
	margin-top: 12px;
	display: flex;
}

.login-scan-error .title {
	font-size: 16px;
	margin-bottom: 16px;
	font-weight: 700;
}

.login-scan-error .line {
	font-size: 15px;
	margin-bottom: 14px;
}

/* 进度loading */
.el-loading-mask.login-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	position: fixed;
	z-index: 9999;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
}

.el-loading-mask.login-loading .el-loading-spinner {
	display: flex;
	align-items: center;
	width: auto;
	min-width: 180px;
	max-width: 700px;
	padding: 16px 25px;
	background-color: #fff;
	border: 1px solid #d3d4d3;
	border-radius: 3px;
	word-break: break-all;
}

.el-loading-mask.login-loading .el-loading-spinner .svgtofont-loading {
	width: 24px;
	font-size: 24px;
	margin-right: 16px;
	color: #555;
	animation: none;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.el-loading-mask.login-loading .el-loading-spinner .svgtofont-loading:after {
	content: '';		
	position: absolute;
	display: block;
	width: 24px;
	height: 24px;
	border: 1.5px solid #20a53a;
	border-top-color: transparent;
	border-radius: 100%;
	animation: circle infinite 0.6s linear;
	opacity: 0.8;
}

.el-loading-mask.login-loading .el-loading-spinner .svgtofont-loading:before{
	content: "";
}


@keyframes circle {
	0% {
		transform: rotate(0);
	}
	to {
		transform: rotate(360deg);
	}
}

.el-loading-mask.login-loading .svgtofont-loading svg {
	height: 16px;
}

.el-loading-mask.login-loading .el-loading-spinner{
	display: flex;
	align-items: center;
}


.el-loading-mask.login-loading .el-loading-spinner .el-loading-text {
	margin: 0;
	line-height: 24px;
	text-align: left;
	font-size: 14px;
	color: #555;
}

/* 消息提示 */
.el-tooltip__popper {
	position: absolute;
	border-radius: 4px;
	padding: 10px;
	z-index: 2000;
	font-size: 12px;
	line-height: 1.2;
	min-width: 10px;
	word-wrap: break-word;
}

.el-tooltip-white {
	background: linear-gradient(180deg, rgba(255, 249, 236, 0.4) 15.727%, rgba(255, 255, 255, 0) 100%) !important;
	background-color: #fff !important;
	color: #666 !important;
	padding: 1.6rem 1.5rem !important;
	border-radius: 0.4rem !important;
	box-shadow: 1px 2px 11px rgba(0, 0, 0, 0.2) !important;
	padding: 1.6rem !important;
	background: #ffffff !important;
}

.el-tooltip-white .popper__arrow {
	border-bottom-color: #fffdf8 !important;
	border-top-color: #fff !important;
	border-bottom-color: #fff !important;
}

.el-tooltip-white .popper__arrow:after {
	border-top-color: #fffdf8 !important;
	border-bottom-color: #fffdf8 !important;
	border-top-color: #fff !important;
	border-bottom-color: #fff !important;
}

.el-tooltip__popper .popper__arrow,
.el-tooltip__popper .popper__arrow:after {
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	border-color: transparent;
	border-width: 6px;
}
/* 
.el-tooltip__popper .popper__arrow:after {
	content: ' ';
	border-width: 5px;
} */
.el-tooltip__popper[x-placement^='top'] .popper__arrow {
	bottom: -6px;
	border-top-color: #555;
	border-bottom-width: 0;
}
.el-tooltip__popper .popper__arrow {
	border-width: 6px;
}

/* 消息通知 */

.el-message {
	top: 50%;
	left: 50%;
	align-items: start;
	min-width: 180px;
	padding: 10px 8px;
	border: 1px solid #d3d4d3;
	background-color: #fff;
	transition:
		top 0s,	
		opacity 0.3s,
		transform 0.4s;
	padding-right: 24px;
	max-width: 400px;
	transform: translateX(-50%) translateY(-50%);
	position: fixed;
	display: flex;
	align-items: center;
	border-radius: 4px;
	box-shadow: 0px 0px 4px 4px #efefef;
}


.el-message .el-dialog-close{
	position: absolute;
	transition: all;
	top: -1.5rem;
	right: -1.5rem;
	z-index: 999;
	width: 3rem;
	height: 3rem;
	cursor: pointer;
	border-radius: 50%;
	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
	transition: transform .4s ease-in-out;
	background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTAiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxjaXJjbGUgc3R5bGU9ImZpbGw6I2ZmZiIgY3g9IjI1IiBjeT0iMjUiIHI9IjI1Ii8+PGNpcmNsZSBzdHlsZT0iZmlsbDojNzg3ODc4IiBjeD0iMjUiIGN5PSIyNSIgcj0iMjAuNSIvPjxwYXRoIHN0eWxlPSJmaWxsOiNmZmYiIGQ9Im0zMi4wNzEgMzQuODk5LTE2Ljk3LTE2Ljk3YTIgMiAwIDEgMSAyLjgyOC0yLjgyOEwzNC45IDMyLjA3MmEyIDIgMCAxIDEtMi44MjkgMi44Mjd6Ii8+PHBhdGggc3R5bGU9ImZpbGw6I2ZmZiIgZD0ibTM0Ljg5OSAxNy45MjktMTYuOTcgMTYuOTdhMiAyIDAgMSAxLTIuODI4LTIuODI4TDMyLjA3MiAxNS4xYTIgMiAwIDEgMSAyLjgyNyAyLjgyOXoiLz48L3N2Zz4=);
}

.el-message .el-dialog-close:hover{
	transform: rotate(180deg);
	background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTAiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxjaXJjbGUgc3R5bGU9ImZpbGw6I2ZmZiIgY3g9IjI1IiBjeT0iMjUiIHI9IjI1Ii8+PGNpcmNsZSBzdHlsZT0iZmlsbDojZjEyOTMwIiBjeD0iMjUiIGN5PSIyNSIgcj0iMjAuNSIvPjxwYXRoIHN0eWxlPSJmaWxsOiNmZmYiIGQ9Im0zMi4wNzEgMzQuODk5LTE2Ljk3LTE2Ljk3YTIgMiAwIDEgMSAyLjgyOC0yLjgyOEwzNC45IDMyLjA3MmEyIDIgMCAxIDEtMi44MjkgMi44Mjd6Ii8+PHBhdGggc3R5bGU9ImZpbGw6I2ZmZiIgZD0ibTM0Ljg5OSAxNy45MjktMTYuOTcgMTYuOTdhMiAyIDAgMSAxLTIuODI4LTIuODI4TDMyLjA3MiAxNS4xYTIgMiAwIDEgMSAyLjgyNyAyLjgyOXoiLz48L3N2Zz4=);
}

.el-message .el-message__content {
	flex: 1;
	padding: 3px 0;
	line-height: 24px;
	font-size: 14px;
	color: #555;
	margin-left: 5px;
}

.el-message .el-message__content h2{
	margin-top: 5px;
}

.el-message .el-message__icon {
	font-size: 30px;
}

.el-message .el-icon-success {
	color: #67c23a;
}

.el-message .el-icon-error {
	color: #f56c6c;
}

.el-divider--vertical {
	display: inline-block;
	width: 1px;
	height: 1em;
	margin: 0 8px;
	vertical-align: middle;
	position: relative;
}

.el-divider {
	background-color: #dcdfe6;
	position: relative;
}


.cursor-pointer{
	cursor: pointer;
}

