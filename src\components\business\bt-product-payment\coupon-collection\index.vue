<template>
	<section class="bg-voucher flex flex-col">
		<div class="pt-2.6rem bottom-bg">
			<div class="px-3.6rem">
				<div class="text-2rem text-[#FFB04C]">免费限时领取【大额优惠券】</div>
				<div class="voucher-list mt-2.6rem">
					<div v-for="(item, index) in couponItem" :key="index" class="voucher-item flex items-center justify-between py-[1.6rem] rounded-[4px]" :class="index === couponItem.length - 1 ? 'mb-[1.4rem]' : 'mb-[2.4rem]'">
						<div class="voucher-amount inline-block px-1.6rem relative w-12rem">
							<div class="font-bold text-[1.8rem]">
								￥<span class="text-[3.4rem]">{{ item.val2 }}</span>
							</div>
							<div class="text-[#999] mt-1rem">满{{ item.val1 }}减{{ item.val2 }}</div>
						</div>
						<div class="inline-block bg-[#FEFBF6] text-left mr-1rem text-[#666] voucher-instructions flex-1 pl-1.6rem">
							<div class="text-[1.4rem] mb-1rem">仅限{{ item.name }}</div>
							<div class="voucher-count-down mt-1.2rem text-left">
								领取后有效期
								<div class="text-danger mlr-[1rem] inline-block text-[2rem]">7</div>
								天
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="px-[3.6rem] pb-1rem mt-1.4rem">
			<div class="voucher-count-down text-center text-[1.6rem] leading-[3rem] mt-[2.6rem] text-[#666]">
				距结束<span>{{ countdown.hour }}</span
				>时<span>{{ countdown.minute }}</span
				>分<span>{{ countdown.second }}</span
				>秒
			</div>
			<div class="mt-[2rem] voucher-footer text-center">
				<span class="text-[#fff] bg-[#FFB04C] hover:(bg-[#F68900])" @click="claimCouponEvent()">一键领取</span>
				<bt-select v-model="timeClose" :options="siteList" class="reminder mt-1rem !w-[14rem]" placeholder="选择不通知的时间" @change="changeCloseTimeEvent" />
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import PRODUCT_PAYMENT_STORE from '../store'

const store = PRODUCT_PAYMENT_STORE()
const { couponItem, closeAfterReload, timeClose, timeNow } = storeToRefs(store)
const { siteList, addZero, claimCouponEvent, changeCloseTimeEvent } = store

interface Props {
	compData: {
		isCoupon: any[]
		isHome: boolean
	}
}

const props = withDefaults(defineProps<Props>(), {
	compData: {
		isCoupon: [],
		isHome: false,
	},
})

const timestamp = useTimestamp() // 响应式获取当前时间戳

watch(
	() => timestamp.value,
	val => {
		timeNow.value = Math.floor(val / 1000)
	}
)

// 倒计时
const countdown = computed(() => {
	// eslint-disable-next-line init-declarations
	let hour: string
	// eslint-disable-next-line init-declarations
	let minute: string
	// eslint-disable-next-line init-declarations
	let second: string
	let endTime = Number(sessionStorage.getItem('HOME-ENDTIME')) // 获取结束时间
	if (!endTime) {
		// 如果没有结束时间，设置当前时间戳加24小时的时间戳为结束时间
		endTime = timeNow.value + 24 * 60 * 60
		sessionStorage.setItem('HOME-ENDTIME', endTime.toString())
	}
	const diff = endTime - timeNow.value // 倒计时剩余时间
	if (diff > 0) {
		hour = addZero(Math.floor(diff / 3600))
		minute = addZero(Math.floor((diff % 3600) / 60))
		second = addZero(diff % 60)
	} else {
		hour = '00'
		minute = '00'
		second = '00'
		if (diff === 0) {
			sessionStorage.removeItem('HOME-ENDTIME')
			changeCloseTimeEvent('week', false)
		}
	}
	return {
		hour,
		minute,
		second,
	}
})

onMounted(() => {
	if (props.compData.isCoupon.length) {
		couponItem.value = props.compData.isCoupon
	}
})
</script>

<style lang="css" scoped>
.bg-voucher {
	background: url('/static/images/advantage/bg-voucher.png') no-repeat;
	background-size: 100%;
	background-position: bottom;
}

.bottom-bg {
	@apply bg-white;
	border-top-left-radius: 0.8rem;
	border-top-right-radius: 0.8rem;
}

.parting-line {
	@apply w-full h-0 relative flex justify-between;
}

.line {
	@apply flex-1 border-dotted border-[#DCDFE6] border-t-width-[0.2rem] h-0;
}

.parting-line-before {
	@apply w-[1.4rem] h-[2.8rem] overflow-hidden relative right-0 -top-[1.4rem];
}

.parting-line-before::after {
	@apply content-[''] relative w-[2.8rem] h-[2.8rem] rounded-full inline-block -left-[1.4rem];
	background: rgba(0, 0, 0, 0.08);
}

.parting-line-after {
	@apply w-[1.4rem] h-[2.8rem] overflow-hidden relative right-0 -top-[1.4rem];
}

.parting-line-after::after {
	@apply content-[''] relative w-[2.8rem] h-[2.8rem] rounded-full inline-block;
	background: rgba(0, 0, 0, 0.08);
}

.voucher-item {
	@apply relative;
	border: 1px solid #f4d9aa;
	background: rgba(255, 248, 238, 0.6);
}

.voucher-item::before {
	@apply content-[''] inline-block w-[4.4rem] h-[4.4rem] top-[0.4rem] left-0 -mt-[.5rem] absolute;
	background: url('/static/images/advantage/bg-voucher-left-icon.svg') no-repeat;
}

.voucher-amount {
	background: linear-gradient(to bottom, rgb(255, 167, 0), rgb(253, 202, 68));
	-webkit-background-clip: text;
	color: transparent;
}

.voucher-amount::after {
	position: absolute;
	content: '';
	display: block;
	width: 0.1rem;
	height: 4.7rem;
	background: #fdca44;
	right: 0;
	bottom: 0.8rem;
}

.voucher-count-down :deep(span) {
	@apply text-danger rounded-[0.2rem] font-bold text-[1.8rem];
	background: rgba(239, 8, 8, 0.08);
	padding: 0.2rem 0.4rem;
	margin: 0 1rem;
}

.voucher-footer span {
	@apply h-[4.2rem] leading-[2.2rem] text-[1.6rem] py-[0.8rem] w-full block text-center rounded-[2rem] cursor-pointer;
}

.voucher-footer span:first-child {
	box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 0, 0, 0.2);
}

:deep(.reminder.el-select .el-select__wrapper) {
	@apply shadow-none w-14rem text-[#999];
}
</style>
