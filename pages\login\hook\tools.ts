/* eslint-disable no-restricted-syntax */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable no-undef */
/* eslint-disable no-underscore-dangle */

export default class Tools {
	autoLoginParams: AnyObject = {
		password: '',
		username: '',
	}

	static svgLogin = `<svg style="font-size: 20px;" version="1.1" xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 93 100" style="enable-background:new 0 0 93 100" xml:space="preserve"><style>.st0{fill:#20a43a}.st1{fill:#18842d}</style><path class="st0" d="M46.6 99.9c-.2-.4-.2-.9-.3-1.4V33.3c0-.7 0-1.4-.1-2.1 0-.1 0-.2.2-.2s.4 0 .6.1c2.1.6 4.1 1.2 6.2 *******.7.4.6.9v4.8c0 .******* 1 1.3.3 2.6.7 3.9.9h.1c.5-.2.8.1 1.2.3h.1c.9 0 .1-.4.3-.6-.1-1.4 0-2.7 0-4.1 0-.3 0-.7.3-.9h.2c2.1.6 4.1 1.2 6.2 *******.4.4.4.7v4.7c0 .*******.9 1.2.3 2.5.6 3.7 1h.1c.6-.2 1.1.3 ******* 0 .2-.1.2-.2.2-.7 0-1.4.1-2.1.1-.9 0-1.8 0-2.6v-.2c0-.3.1-.2.3-.2 2 .6 4.1 1.2 6.1 *******.******* 0 5.1.1 10.2.1 15.3 0 6.3 0 12.6.1 18.9 0 .7-.2 1.5-.5 2.1-1.7 3.3-4 6-6.8 8.5-5 4.5-10.8 7.6-17 10.2-2.9 1.2-5.8 2-8.7 3.1-.2.1-.3.1-.3.1h-.1c-.2.1-.4.2-.6.2"/><path class="st1" d="M46.2 31.1c.2 0 .4.2.4.4v68.6l-.2-.1s-4-1.3-6-2c-6.9-2.5-13.4-5.6-19-10.3-3.2-2.6-5.9-5.5-7.9-9.1-.6-1-.9-2.2-.9-3.3.1-11.2.1-22.4.1-33.6 0-.7.2-.9.8-1.1 2-.5 3.9-1.1 5.9-1.7.3.3.2.7.2 1v3.3c0 .8.1.9.9.7.3-.1.6-.1.9 0 1.3-.3 2.6-.7 4-1 .5-.1.5-.4.5-.8v-4.8c0-.3 0-.6.4-.7l6.3-1.8c.3.3.2.7.2 1.1 0 1.4-.1 2.8.1 4.3.5.4.9-.1 1.4-.1.2-.1.4 0 .7 0 1.2-.3 2.4-.7 3.6-1 .6-.1.8-.4.8-1-.1-1.6 0-3.2 0-4.9 0-.3 0-.6.4-.7 2.1-.3 4.3-.9 6.4-1.4z"/><path class="st0" d="M46.6 15.6c1 .1 1.7.5 1.8 1.6.1.9.2 2.1.8 2.7.6.6 1.8.6 2.7.9 11.3 3.2 22.6 6.4 33.9 9.5.6.2 1 .2 1.5-.3l.1-.1c1.6-1.6 3.4-2.1 5.6-1.2.1.2-.2.3-.3.5-1.8 2.4-3.6 4.7-5.4 7.1-.3.5-.6.6-1.2.4-12.9-3.8-25.9-7.5-38.8-11.2-.4-.1-.8-.2-1.3-.1.3-1 .2-2.1.2-3.1v-5.4c.1-.6.1-1 .4-1.3z"/><path class="st1" d="M46.6 15.6v8.7c0 .5 0 .8-.5 1-3.6 1-7.1 2.1-10.6 3.1-9.5 2.7-19 5.5-28.4 8.2-.3.1-.7.3-1-.1-2-2.6-4-5.2-6.1-7.9l3.1-.4c.2 0 .3 0 .5.1s.3.2.4.3c1.6 1.7 3.4 1.7 5.6 1.1 11.2-3.3 22.5-6.4 33.7-9.5.5-.2.9-.4 1-1 .1-.7.4-1.4.5-2.1.1-1.1.8-1.5 1.8-1.5z"/><path class="st0" d="M46.6.2c1.6-.1 1.6-.1 1.9 1.4.4 1.9.7 3.9 1.1 5.8 0 .4.3.7.7.8 10.5 3.2 21 6.3 31.5 9.5.3.1.7.1.9-.2l.1-.1c1.7-1.5 3.6-2.3 5.9-1.2 0 .2-.3.3-.4.4-2 2-4.1 3.9-6 5.9-.5.5-.9.6-1.6.4-11.1-3.1-22.3-6.2-33.4-9.3-.3-.1-.6-.1-.8-.2-.1-.3-.2-.6-.1-.8V1.5c-.1-.4-.2-.9.2-1.3z"/><path class="st1" d="M46.6.2v12.3c0 .3 0 .7-.2 1-5.5 1.5-10.9 3.1-16.4 4.6-5.8 1.6-11.6 3.2-17.4 4.9-.8.2-1.2.1-1.8-.5-2-2.2-4.1-4.4-6-6.5.6-.1 1.6-.2 2.6-.4.4-.1.7.2.9.4 1.7 1.7 3.4 1.6 5.6.9 9.5-3 19.2-5.8 28.7-8.7.6-.2.9-.5 1-1.1.3-2 .8-3.9 1.1-5.9.1-.9.4-1.3 1.4-1.1.2.1.3.1.5.1z"/><path class="st0" d="M21.5 44.1c-.5.2-1.1.4-1.7.5-.5.1-.4-.4-.4-.6v-4.9h.1c2 .3 2 .3 2 2.3-.1.8 0 1.7 0 2.7z"/><path d="M73.7 39v.3c.1 1.8.1 3.5 0 5.3-.7-.1-1.4-.4-2.1-.5v-3.8c0-.6.2-.9.8-1 .5-.1.9-.4 1.3-.3zm-14 1.7-1.2-.3v-4.2c0-.6.2-.9.8-.9.4 0 .8-.3 1.3-.2-.3.7-.1 1.5-.2 2.2v3.1c.1.4.1.5-.7.3z" style="fill:#18852d"/><path d="M34.3 40.5c.2 0 .5-.1.7-.1-.5 0-.3-.4-.3-.6v-3.7c0-.3.1-.6-.3-.6-.6-.1-1.1-.4-1.7-.3v5c0 .1 0 .8.2.9h.3c.1 0 .2 0 .3-.1" style="fill:#20a53a"/><path d="M73.7 44.6v-5.3c.3.2.2.6.2.9.1 1.4-.1 2.9.1 4.3 0 .1-.1.1-.1.2 0 0-.1-.1-.2-.1z" style="fill:#24a53e"/></svg>`

	// 检测当前路由是否包含自动登录字段
	public checkAutoLogin() {
		return new Promise((resolve, reject) => {
			const url = new URL(window.location.href)
			if (url.search !== '') {
				const params = new URLSearchParams(url.search)
				params.forEach((value, key) => {
					this.autoLoginParams[key] = value
				})
				if (this.autoLoginParams.username && this.autoLoginParams.password) {
					resolve(this.autoLoginParams)
				}
				// else {
				// 	reject(new Error('未检测到自动登录字段'))
				// }
			}
			// else {
			// 	reject(new Error('未检测到自动登录字段'))
			// }
		})
	}

	/**
	 * @description 事件绑定
	 * @param {HTMLElement} element  DOM元素
	 * @param {string} eventType 事件类型
	 * @param {(ev: Event) => void} handler 事件处理函数
	 */
	public bindEvent(element: HTMLElement | null, eventType: string, handler: (ev: Event) => void): void {
		if (element && element.addEventListener) {
			element.addEventListener(eventType, handler, false)
		} else if (element && (element as any).attachEvent) {
			;(element as any).attachEvent(`on${eventType}`, handler || null)
		} else {
			;(element as any)[`on${eventType}`] = handler || null
		}
	}

	/**
	 * @description 事件代理
	 * @param {HTMLElement} parentElement
	 * @param {string} eventType 事件类型
	 * @param {string | EventListener} selector 选择器
	 * @param {EventListener} handler 事件处理函数
	 */
	public delegateEvent(parentElement: HTMLElement | null, eventType: string, selector: string | EventListener, handler?: EventListener): void {
		function closestAttr(el: HTMLElement, attr: string) {
			while (el) {
				if (el.hasAttribute(attr)) return el
				el = el.parentElement as HTMLElement
			}
			return null
		}

		if (typeof selector === 'string' && parentElement) {
			parentElement.addEventListener(
				eventType,
				function (event) {
					const targetElement = event.target as HTMLElement
					const selectedElement = targetElement.closest(selector)
					if (this.contains(selectedElement)) {
						if (handler) handler.call(selectedElement, event)
					}
				},
				false
			)
		} else if (typeof selector !== 'string') {
			this.bindEvent(parentElement, eventType, selector as EventListener)
		}
	}

	/**
	 * @description 值类型转换
	 * @param {string|number} value 值
	 * @param {string|number} targetType 目标类型
	 * @returns {string|number}
	 */
	private convertType(value: any, targetType: 'string' | 'number'): string | number {
		const currentType = typeof value
		if (currentType === targetType) return value
		if (targetType === 'string') return String(value)
		if (targetType === 'number') return Number(value)
		throw new Error(`Unsupported target type: ${targetType}`)
	}

	/**
	 * @description 渲染select
	 * @param {string} data.el select的DOM元素
	 * @param {Array} data.options select的数据列表
	 * @param {AnyFunction} data.change select的change事件
	 */
	public renderSelect(data: { el: HTMLDivElement; options: { key: string | number; label: string; disabled?: boolean }[]; active?: string | number; size?: string; change?: (key: string, index: number) => void }) {
		const { el, options, active, change, size } = data
		const selectDisplay = document.createElement('div')
		const selectList = document.createElement('div')

		el.classList.add('form-select-module')
		el.innerHTML = ''
		if (size) el.classList.add(size)
		selectList.classList.add('select-list')

		// 判断配置是否存在值
		if (options.length > 0) {
			let activeBak = active
			if (typeof active === 'undefined') activeBak = options[0].key as string
			for (let i = 0; i < options.length; i++) {
				const select = document.createElement('div')
				select.classList.add('select-item')
				if (typeof options[i].disabled === 'boolean' && options[i].disabled) {
					select.classList.add('disabled')
				}
				select.innerText = options[i].label
				select.dataset.key = options[i].key as string
				select.dataset.disabled = `${Number(options[i].disabled)}`
				if (options[i].key === activeBak) {
					select.classList.add('active')
					selectDisplay.innerText = options[i].label
				}
				selectList.appendChild(select)
			}
		}

		selectDisplay.classList.add('select-display')
		el.appendChild(selectDisplay)
		el.appendChild(selectList)

		// 定义全局事件处理器
		const globalClickHandler = (ev: any) => {
			if (ev.target.classList.contains('disabled') && ev.target.classList.contains('select-item')) {
				this.message({ message: '当前选项不可选', type: 'error', time: 2000 })
			} else {
				el.classList.remove('active')
				window.removeEventListener('click', globalClickHandler)
			}
		}

		// 点击打开下拉
		this.delegateEvent(selectDisplay, 'click', (e: Event) => {
			e.stopPropagation() // 阻止事件冒泡
			el.classList.toggle('active')
			if (el.classList.contains('active')) {
				window.addEventListener('click', globalClickHandler)
			} else {
				window.removeEventListener('click', globalClickHandler)
			}
		})

		// 点击下拉选项
		this.delegateEvent(selectList, 'click', '.select-item', (e: Event) => {
			const target = e.target as HTMLElement
			const { key, disabled } = target.dataset
			const label = target.innerText
			if (Number(disabled)) return false
			const index = options.findIndex(item => item.key === this.convertType(key, typeof item.key))
			const select = selectList.querySelectorAll('.select-item')
			select.forEach((item, indexs) => {
				if (index === indexs) {
					item.classList.add('active')
				} else {
					item.classList.remove('active')
				}
			})
			selectDisplay.innerText = label
			el.classList.remove('active')
			el.dataset.key = key
			if (change) change(key, index)
		})
		// 注销全局事件
		window.removeEventListener('click', globalClickHandler)
		return el.dataset.key
	}

	/**
	 * @description 表单验证
	 * @param {HTMLFormElement} data.el 表单DOM元素
	 * @param {AnyObject} data.rules 表单验证规则
	 * @returns {boolean}
	 */
	public validateForm(data: { el: HTMLFormElement; rules: AnyObject }) {
		const { rules } = data
		const ruleList: AnyObject = {}
		const nodeList: AnyObject = {}
		for (const key in rules) {
			if (Object.prototype.hasOwnProperty.call(rules, key)) {
				nodeList[key] = document.querySelector(`[name="${key}"]`)
				const rule = rules[key]
				if (rule.trigger) {
					rule.trigger.forEach((item: string) => {
						let errorVerify: boolean = false
						// eslint-disable-next-line consistent-return
						ruleList[key] = (keys: string): Error | boolean => {
							const that = nodeList[keys]
							rule.validator(that.value.trim(), function (error: string | undefined) {
								try {
									throw new Error(error)
								} catch (error) {
									errorVerify = false
									const errorTips = error.message.replace('Error: ', '') // 判断是否有错误提示
									that.parentElement?.querySelector('.error-tips')?.remove()
									that.parentElement?.classList.remove('error')
									if (errorTips) {
										errorVerify = true
										that.parentElement?.classList.add('error')
										that.insertAdjacentHTML('afterend', `<div class="error-tips">${errorTips}</div>`)
									}
								}
							})
							if (errorVerify) return new Error(`${keys}:${nodeList[keys].vlaue}`)
							return true
						}
						this.bindEvent(nodeList[key], item, function () {
							if ('isShow' in rules[key] && !rules[key].isShow()) {
								return true
							}
							const name = this.getAttribute('name')
							ruleList[name](name)
						})
					})
				}
			}
		}
		return (fn: AnyFunction) => {
			let isVerifyError = 0
			for (const key in ruleList) {
				if (Object.prototype.hasOwnProperty.call(ruleList, key)) {
					const element = ruleList[key]
					try {
						if ('isShow' in rules[key] && !rules[key].isShow()) continue
						const error = element(key)
						if (typeof error === 'object') throw error
					} catch (error) {
						isVerifyError++ // 验证错误
						nodeList[key].focus() // 获取焦点，显示错误提示
						return false // 避免循环验证
					}
				}
			}

			if (!isVerifyError) {
				const formData: AnyObject = {}
				for (const key in nodeList) {
					if (Object.prototype.hasOwnProperty.call(nodeList, key)) {
						const element = nodeList[key]
						const { value } = element
						formData[key] = value.trim()
					}
				}
				fn(formData)
			}
		}
	}

	/**
	 * @description 处理请求数据
	 * @param { AnyObject } oldData
	 * @returns
	 */
	private handleTransformRequest(oldData: AnyObject): string {
		let newStr = ''
		// eslint-disable-next-line no-restricted-syntax
		for (const key in oldData) {
			if (Object.prototype.hasOwnProperty.call(oldData, key)) {
				const value = oldData[key]
				newStr += `${encodeURIComponent(key)}=${encodeURIComponent(value)}&`
			}
		}
		newStr = newStr.slice(0, -1)
		return newStr
	}

	/**
	 * @description 请求方法-POST
	 * @param {string} config.url 请求地址
	 * @param {AnyObject} config.data 请求参数,默认为{}
	 * @returns
	 */
	public async request<T>({ url, data, method }: AnyObject = { method: 'post', data: {}, url: '' }) {
		return new Promise<any>((resolve, reject) => {
			const xhr = new XMLHttpRequest()
			xhr.open(method, url, true)
			xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded')
			xhr.timeout = 250000
			xhr.onreadystatechange = () => {
				if (xhr.readyState === 4) {
					if (xhr.status >= 200 && xhr.status < 300) {
						try {
							resolve({
								...xhr,
								data: JSON.parse(xhr.responseText),
							})
						} catch (error) {
							resolve({
								...xhr,
								data: xhr.responseText,
								error,
							})
						}
					} else {
						reject(new Error(xhr.statusText))
					}
				}
			}
			xhr.ontimeout = () => reject(new Error('Request timed out'))
			xhr.onerror = () => reject(new Error('Network error'))
			xhr.send(this.handleTransformRequest(data))
		})
	}

	/**
	 * @description rsa加密
	 * @param { string } str 需要加密的字符串
	 * @returns { string } 加密后的字符串
	 */
	private async rsaEncrypt(str: string): Promise<string> {
		const { JSEncrypt } = window
		const publicKey = window.vite_public_encryption
		if (publicKey.length < 10) return str
		const encrypt = new JSEncrypt()
		encrypt.setPublicKey(publicKey)
		return encrypt.encrypt(str) as string
	}

	/**
	 * @description 加密数据
	 * @param {AnyObject} data
	 * @return {AnyObject} 加密后的数据
	 */
	public async encryptionData(data: any): Promise<AnyObject> {
		const { md5 } = window
		data.username = await this.rsaEncrypt(md5(md5(data.username + window.vite_public_login_token)))
		data.password = await this.rsaEncrypt(md5(`${md5(data.password)}_bt.cn`))
		return data
	}

	/**
	 * @description 移除空值
	 * @param {[key: string]: any} obj 需要移除空值的对象
	 * @returns {[key: string]: any} 移除空值后的对象
	 */
	removeEmpty(obj: { [key: string]: any }): { [key: string]: any } {
		return Object.entries(obj).reduce((a: { [key: string]: any }, [k, v]) => {
			if (v != null && v !== '') a[k] = v
			return a
		}, {})
	}

	/**
	 * @description 加载loading
	 * @param {string} text loading提示信息
	 * @returns {AnyObject} loading对象
	 */
	public loading(text: string) {
		const template = `<div id="full-loading" class="el-loading-mask login-loading" style="background-color: rgba(0, 0, 0, 0.3); z-index: 3006;">
			<div class="el-loading-spinner">
				<i class="svgtofont-loading">${Tools.svgLogin}</i>
				<p class="el-loading-text">${text}</p>
			</div>
		</div>`
		window.document.body.insertAdjacentHTML('beforeend', template)
		return {
			close() {
				const loading = document.getElementById('full-loading')
				if (loading) loading.remove()
			},
		}
	}

	/**
	 * @description 消息提示
	 * @param {string} data.message 提示信息
	 * @param {string} data.type 提示类型
	 * @param {number} data.time 提示时间
	 */
	public message({ message, type, time = 3000, close = false }: { message: string; type: 'success' | 'error'; time?: number; close?: boolean }) {
		document.getElementById('el-message')?.remove()
		const iconType = type === 'success' ? 'circle-check-filled' : 'circle-close-filled'
		const template = `<div id="el-message" class="el-message el-message--${type}" style="z-index: 4001;">
				${close ? '<div class="el-dialog-close"></div>' : ''}
				<div class="el-message__icon loading-icon svgtofont-el-${iconType}"></div>
				<div class="el-message__content">${message || ''}</div>
			</div>`
		window.document.body.insertAdjacentHTML('beforeend', template)
		if (time) {
			setTimeout(() => {
				const message = document.getElementById('el-message')
				if (message) message.remove()
			}, time)
		}
		if (close) {
			this.bindEvent(document.querySelector('.el-dialog-close'), 'click', () => {
				document.getElementById('el-message')?.remove()
			})
		}
	}

	/**
	 * @description 消息提示
	 */
	public tooltip({ el, text }: { el: HTMLElement; text: string }) {
		document.getElementById('el-tooltip')?.remove()
		const template = `<div  id="el-tooltip" x-placement="top" class="el-tooltip__popper is-dark el-tooltip-white overview-popover w-[40rem] custom-login-popover" style="transform-origin: center bottom; z-index: 3009; display: none;">
				<div class="leading-8 p-[8px]">${text}</div>
				<div class="popper__arrow" style="left: 154px;"></div>
		</div>`
		window.document.body.insertAdjacentHTML('beforeend', template)
		const elTooltip = document.getElementById('el-tooltip') as HTMLDivElement
		let closeTime = 0
		// 在触发按钮鼠标移入
		this.bindEvent(el, 'mouseenter', (ev: MouseEvent) => {
			clearTimeout(closeTime)
			elTooltip.style.display = 'block'
			elTooltip.style.left = `${ev.clientX - ev.offsetX - 150}px`
			elTooltip.style.top = `${ev.clientY - ev.offsetY - elTooltip.clientHeight - 10}px`
		})

		// 在触发按钮鼠标移出
		this.bindEvent(el, 'mouseleave', () => {
			clearTimeout(closeTime)
			closeTime = setTimeout(() => {
				elTooltip.style.display = 'none'
			}, 100)
		})

		// 在提示上鼠标移入
		this.bindEvent(elTooltip, 'mouseenter', () => {
			clearTimeout(closeTime)
			elTooltip.style.display = 'block'
		})

		// 在提示上鼠标移出
		this.bindEvent(elTooltip, 'mouseleave', () => {
			clearTimeout(closeTime)
			closeTime = setTimeout(() => {
				elTooltip.style.display = 'none'
			}, 100)
		})

		elTooltip.style.left = `calc(50% - ${elTooltip.offsetWidth / 2}px)`
		elTooltip.style.top = `calc(50% - ${elTooltip.offsetHeight / 2}px)`
	}
}
