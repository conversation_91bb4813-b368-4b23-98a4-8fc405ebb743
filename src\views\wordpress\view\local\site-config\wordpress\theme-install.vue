<template>
	<div class="p-[2rem]">
		<BtTableGroup>
			<template #header-right>
				<BtSearch type="local-setting-theme" placeholder="请输入主题名称" class="!w-[270px] mr-[10px]" />
			</template>
			<template #content><BtTable :max-height="500" /></template>
			<template #footer-right><BtPage /></template>
		</BtTableGroup>
	</div>
</template>

<script setup lang="tsx">
import { Message, useAllTable, useDataHandle, useRecommendSearch, useRefreshList } from '@/hooks/tools'
import useWPWordpressSettingStore from './useStore'
import { getThemeInstallList } from './useController'
import BtRate from '@/components/form/bt-rate'
import { useOperate } from '@/hooks/tools/table/column'
import useWPWordpressLocalStore from '@/views/wordpress/view/local/useStore'
import { setInstallThemes } from '@/api/wp'

const { localRow } = storeToRefs(useWPWordpressLocalStore())
const { isRefreshThemeInstall, isRefreshThemeList } = storeToRefs(useWPWordpressSettingStore())

/**
 * @description 获取表格
 */
const { BtTable, BtPage, BtRecommendSearch, BtRefresh, BtColumn, BtBatch, BtTableCategory, BtErrorMask, tableRef, classList, refresh, BtSearch } = useAllTable({
	request: (data: any) => {
		return getThemeInstallList(data)
	},
	columns: [
		{
			label: '名称',
			prop: 'name',
			minWidth: 180,
			isCustom: true,
			render: (row: any) => {
				return (
					<span class="flex items-center">
						<img class="w-[25px] h-[25px]" src={row.screenshot_url} />
						<span class="ml-[5px]">{row.name}</span>
					</span>
				)
			},
		},
		{
			label: '描述',
			width: 200,
			prop: 'description',
			showOverflowTooltip: true,
		},
		{
			label: '作者',
			prop: 'author',
			render: (row: any) => (
				<a target="_blank" style="color : #1d9534" href={row.author.author_url}>
					{row.author.author}
				</a>
			),
		},
		{
			label: 'WP',
			prop: 'requires',
			width: 50,
			render: (row: any) => (row.requires ? row.requires : '--'),
		},
		{
			label: 'PHP',
			prop: 'requires_php',
			render: (row: any) => (row.requires_php ? row.requires_php : '--'),
		},
		{
			label: '等级',
			prop: 'rating',
			width: 140,
			render: (row: any) => {
				const rating = row.rating ? row.rating / 20 : 0
				return (
					<div class="flex items-center">
						<BtRate modelValue={rating} disabled={true} allow-half={true} class="!text-[2rem]" />
					</div>
				)
			},
		},
		useOperate(
			(row: any) => [
				{
					title: row.installed ? '已安装' : '安装',
					render: (row: any) => {
						const isDisabled = (localRow.value.wp_version as string) < row.requires || (localRow.value.php_version as string) < row.requires_php || row.installed
						return row.installed ? (
							<span class={'text-[#999]'} style={'cursor:not-allowed'}>
								已安装
							</span>
						) : (
							<span
								class={'bt-link'}
								style={isDisabled ? 'cursor:not-allowed; color: #999;' : ''}
								onClick={
									isDisabled
										? undefined
										: () => {
												useDataHandle({
													loading: '安装中...',
													request: setInstallThemes({ s_id: localRow.value.id, slug: row.slug }),
													success: (rdata: any) => {
														if (rdata.status) {
															isRefreshThemeInstall.value = true
															isRefreshThemeList.value = true
															Message.success('安装成功')
														}
													},
												})
										  }
								}>
								安装
							</span>
						)
					},
				},
			],
			{
				label: '操作',
				width: 100,
				fixed: 'right',
			}
		),
	],
	extension: [useRecommendSearch('search', { name: 'local-setting-plugin', list: [] }), useRefreshList(isRefreshThemeInstall)],
})
</script>

<style scoped></style>
