<template>
	<div class="flex flex-col items-center mt-[4%]" :style="{ 'min-height': mainHeight - 200 + 'px' }">
		<BtAlert v-if="forceLtd" class="w-[44rem]" :closable="false" type="warning" title="注意事项：当前面板为企业版，绑定账号后，无法随意解绑或切换账号，绑定成功会赠送14天免费企业版授权。" />
		<BtBindUser v-if="commonBind" class="w-[44rem]" :compData="{ title: '绑定宝塔官网账号', btn: '登录' }" />

		<!-- <div v-if="aliyunEcsLtd" class="advantage-list">
			<span class="text-[1.4rem] text-[#d98704]"> 此版本为阿里云联合宝塔面板定制的Linux企业版，检测到您无法正常登录，请手动尝试或扫描下面二维码联系客服</span>
		</div> -->

		<component class="w-[44rem]" v-if="!commonBind && !aliyunEcsLtd" :compData="{ title: '绑定宝塔官网账号', btn: '登录', authData, onChangeType }" :is="asyncBindComponent"></component>
		<template v-if="bindType === 'bind' && !aliyunEcsLtd">
			<ul class="list-disc help-text-info -ml-7rem">
				<li>为了您能更好的体验面板功能，请先绑定堡塔账号；</li>
				<li>单个宝塔帐号支持多台服务器绑定；</li>
				<li>绑定帐号没有接管服务器的功能权限，请放心使用；</li>
				<li>帐号绑定过程中遇到问题请联系客服处理；</li>
				<li>客服电话：0769-23030556</li>
				<li>
					客服咨询：
					<el-popover placement="bottom" width="150" trigger="hover" popper-class="white-tips-popover" class="inline-block relative leading-[2.4rem]">
						<template #reference>
							<span class="bt-link">查看二维码</span>
						</template>
						<bt-image width="150" :src="`/other/customer-qrcode.png`" alt="客服咨询" />
					</el-popover>
				</li>
			</ul>
		</template>
		<li v-if="bindType === 'other' && !aliyunEcsLtd">
			如果遇到问题，请联系客服处理：
			<el-popover placement="bottom" width="150" trigger="hover" popper-class="white-tips-popover" class="inline-block relative leading-[2.4rem]">
				<template #reference>
					<span class="bt-link">查看二维码</span>
				</template>
				<bt-image width="150" :src="`/other/customer-qrcode.png`" alt="客服咨询" />
			</el-popover>
		</li>
	</div>
</template>

<script lang="ts" setup>
import { useGlobalStore } from '@store/global'
import { checkLoginAuth } from '@api/global'

import AliBindUser from '@/components/business/bt-bind-user/ali-bind-user.vue'
import TencentBindUser from '@/components/business/bt-bind-user/tencent-bind-user.vue'

import BtImage from '@/components/base/bt-image'
import { ElPopover } from 'element-plus'

const { mainHeight, forceLtd, aliyunEcsLtd } = useGlobalStore()
const commonBind = ref(true) // 是否普通面板

const bindType = ref('bind') // bind堡塔账号，other第三方账号
const authData = ref({
	client: 0, // 服务商标识 0：宝塔账号，1：腾讯云，2：阿里云
	login: 0, // 授权登录方式 0：宝塔账号，1：一键登录，2：授权登录
}) // 一键登录权限

const asyncBindComponent = ref<Component | string>('div') // 第三方绑定组件

// // 第三方面板
// const checkExtension = async() => {
// 			if (window.$extension) {
// 				// 第三方面板
// 				const plugin = await window.$extension()
// 				if (plugin.extensionElement) {
// 					commonBind.value = false
// 					plugin.extensionElement({
// 						title: '绑定宝塔官网账号',
// 						btn: '登录',
// 						onChangeType: (type: 'bind' | 'other') => {
// 							bindType.value = type
// 						},
// 					})
// 				}
// 			}
// }

// 切换绑定类型
const onChangeType = (type: 'bind' | 'other') => {
	bindType.value = type
}

// 判断是否有一键登录权限
const checkBindAuth = async () => {
	try {
		const res = await checkLoginAuth()
		if (res.status) {
			if (res.data?.login !== 0) {
				authData.value.client = res.data.client
				authData.value.login = res.data.login
				switch (res.data?.client) {
					case 1:
						asyncBindComponent.value = TencentBindUser
						break
					case 2:
						asyncBindComponent.value = AliBindUser
						break
				}
				commonBind.value = false
			} else {
				commonBind.value = true
			}
		} else {
			commonBind.value = true
		}
	} catch (error) {
		commonBind.value = true
	}
}

nextTick(() => {
	checkBindAuth()
})

onMounted(() => {
	localStorage.clear()
	sessionStorage.clear()
	// checkExtension()
})
</script>

<style lang="css" scoped>
.help-text-info li {
	@apply items-center leading-[2.4rem] text-[#777];
}

.advantage-list {
	box-shadow: 0 0 4px 2px #00000017;
	@apply w-[80%] flex justify-center items-center mt-[2.4rem] mx-[auto] text-[1.2rem] truncate bg-[#FFF7E7] p-[1.6rem] rounded-[0.4rem];
}
</style>
