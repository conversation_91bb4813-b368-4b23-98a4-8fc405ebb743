import { replace } from 'ramda'
import { hasOwnProperty } from './../../utils/data'
import { version } from './../soft/public/environment-plugin/memcached/useController'
import { getSystemInfo as getSystemInfoData, getUpdateInfo as getUpdateInfoApi, getHomeResData, upgradePanelNew } from '@/api/global'
import { useGlobalStore } from '@/store/global'
import { useDialog } from '@hooks/tools'
import { compareVersion, isString } from '@/utils/index'
import { ElNotification } from 'element-plus'
import { defineStore } from 'pinia'
import LoginPrompt from '@home/views/login-prompt/index.vue' // 登录提示
import { App } from 'vue'

const HOME_STORE = defineStore('HOME-STORE', () => {
	const { getGlobalInfo } = useGlobalStore()

	const isRefreshData = ref(0) // 是否获取首页数据
	const recommendInstall = ref<boolean>(false) // 推荐安装
	const systemInfo = ref<AnyObject>({
		name: '', // 系统名称
		simpleName: '', // 系统简称
	})
	const refreshTime = ref<number>(0) // 磁盘,io等动态数据刷新时间
	const panelInfo = ref<AnyObject>({
		runningDays: 0, // 运行天数
		isBeta: false, // 是否是测试版
		isUpgrade: false, // 是否有新版本
		cloudBeta: false, // 云端是否有测试版
		betaInfo: null, // 云端测试版信息
		officialInfo: null, // 云端正式版信息
		grayscale: 1, // 灰度等级:0-1，0.2为20%的灰度，0.5为50%的灰度，1为正常发布
		isSetup: true, // 是否已安装套件
		isRestart: false, // 是否重启
		upgrade: 0, // 0:无更新 1:有推荐更新 2:有最新正式版更新
		currentInfo: null, // 当前更新显示对应版本信息
		accountVersionInfo: null, // 多用户版本信息
	})
	const load = ref<AnyObject>({
		// 负载信息
		one: 0, // 一分钟负载
		max: 0, // 最大负载
		five: 0, // 五分钟负载
		fifteen: 0, // 十五分钟负载
	})
	const cpu = ref<AnyObject>({
		// cpu
		occupy: '', // 占用率
		framework: '', // 框架
		coresOccupy: [0, 0, 0], // 三个核心的占用率
		cpuNumber: 0,
		logicCores: 0,
		cores: 0,
		process: [0, 0],
	})

	const cpuTimes = ref<AnyObject>({}) // CPU详情
	const memory = ref<AnyObject>({
		// 内存
		memRealUsed: 0, // 已使用内存
		memTotal: 0, // 总内存
		memFree: 0, // 空闲内存
		memShared: 0, // 共享内存
		memAvailable: 0, // 可用内存
		memBuffers: 0, // 缓冲区
		memCached: 0, // 缓存
	})
	const specificResource = ref({
		// 状态表格信息数据
		cpuBaseInfo: {
			active_processes: 0,
			cpu_name: 'Common KVM processor * 2',
			load_avg: {},
			logical_cpu: 0,
			num_phys_cores: 0,
			physical_cpu: 0,
			total_processes: 0,
		},
		cpuProcess: [],
		memProcess: [],
	})

	const disk = ref<any>([]) // 磁盘列表
	const diskQuota = ref<any>([]) // 磁盘配额
	const recommendSoft = ref<any>([]) // 推荐软件
	const network = ref<any>([]) // 网络
	const diskIo = ref<any>([]) // 磁盘io
	const diskUsage = ref<any>([]) // 磁盘使用情况
	const overviewShowList = ref<any>([]) // 模板信息

	/**
	 * @description 初始化推荐安装软件，初始化模块，必须提前加载
	 * @returns {App}
	 */
	const recommendInstallDialog = async (): Promise<App> => {
		return useDialog({
			component: () => import('@home/views/rec-install/index.vue'),
			title: '<div class="flex items-center justify-between text-[1.2rem]"><span>初始化推荐配置</span><span class="close-ad">不再显示推荐</span></div>',
			area: 105,
			closeBtn: 2,
		})
	}

	/**
	 * @description: 获取面板更新内容
	 * @param {boolean} data.check 是否检查更新
	 * @param {boolean} data.toUpdate 是否更新版本
	 * @returns {Promise<void>}
	 */
	const getUpdateInfo = async (data: { check?: boolean; toUpdate?: 'yes' } = {}) => {
		try {
			const { data: rdata } = await getUpdateInfoApi(data)
			const { status, msg } = rdata
			if (status && msg) {
				setUpdateInfo(msg)
				return msg
			}
			return false
		} catch (err) {
			console.log(err, 'getUpdateData')
		}
	}

	/**
	 * @description 设置更新信息
	 * @param {any} data 更新信息数据
	 */
	const setUpdateInfo = (data: any) => {
		const locaVersion = window.vite_public_version
		const lastPart = locaVersion.split('.').pop() as string
		const isBeta = parseInt(lastPart).toString() === lastPart && lastPart.length === 2

		const { is_beta: cloudBeta, version, beta, downUrl, updateMsg, uptime, grayscale_test: grayscale } = data
		Object.assign(panelInfo.value, {
			isBeta,
			isUpgrade: !compareVersion(locaVersion, isBeta ? beta.version : version),
			cloudBeta,
			betaInfo: beta,
			officialInfo: { downUrl, updateMsg, version, uptime },
			grayscale,
		})
	}

	/**
	 * @description 获取面板更新内容
	 * @returns {Promise<void>}
	 */
	const getUpdateInfoNew = async () => {
		try {
			const { data } = await upgradePanelNew({ check: true })
			if (data) setUpdateInfoNew(data)
			return data
		} catch (err) {
			console.log(err, 'getUpdateInfoNew')
		}
	}

	/**
	 * @description 设置更新信息
	 * @param {any} data 更新信息数据
	 */
	const setUpdateInfoNew = (data: any) => {
		const { local, cloud, upgrade } = data

		const currentInfo = upgrade === 2 ? cloud?.OfficialVersionLatest : upgrade === 1 ? cloud?.OfficialVersion : local
		const accountVersionInfo = cloud?.AccountVersion
		Object.assign(panelInfo.value, {
			// grayscale,
			upgrade,
			currentInfo,
			accountVersionInfo,
		})
	}

	/**
	 * @description 获取全局信息
	 */
	const getHomeConfig = async () => {
		// debugger
		const data = await getGlobalInfo()
		if (typeof data === 'object' && data) panelInfo.value.isSetup = data.isSetup
		if (data.migration) {
			/**
			 * @description 备份进行中提示弹窗
			 */
			useDialog({
				area: 45,
				component: () => import('@components/business/bt-recover-popup/index.vue'),
			})
		}
	}

	/**
	 * @description 获取系统信息
	 */
	const getSystemInfo = async () => {
		try {
			const rdata = await getSystemInfoData()
			isRefreshData.value = new Date().getTime()
			setSystemInfo(rdata.data)
		} catch (err) {
			console.log(err)
		}
	}

	/**
	 * @description 设置系统信息
	 * @param {any} data 系统信息数据
	 * @returns {void}
	 */
	const setSystemInfo = (data: any) => {
		const { disk: diskList, mem, network: networkData, iostat, cpu: cpuData, installed, cpu_times, down, up, downPackets, upPackets, downTotal, upTotal, system, simple_system: simpleSystem, time: runningDays } = data
		const networkGroup = {
			ALL: { down, up, downPackets, upPackets, downTotal, upTotal },
			...networkData,
		}

		setDiskInfo(diskList) // 磁盘信息处理
		const time = Date.now() // 当前时间
		recommendInstall.value = installed
		memory.value = mem
		network.value = networkGroup
		diskIo.value = iostat
		cpu.value = {
			occupy: cpuData[0],
			cores: cpuData[1],
			coresOccupy: cpuData[2],
			framework: cpuData[3],
			logicCores: cpuData[4],
			cpuNumber: cpuData[5],
			process: [cpu_times['活动进程数'], cpu_times['总进程数']],
		}
		cpuTimes.value = cpu_times
		load.value = data.load
		refreshTime.value = time
		systemInfo.value.name = simpleSystem
		systemInfo.value.simpleName = system
		panelInfo.value.runningDays = runningDays
	}

	/**
	 * @description 构建磁盘信息
	 * @param data
	 */
	const setDiskInfo = (list: any) => {
		const diskList: Array<any> = []
		const quota: Array<any> = []
		list.forEach((data: any) => {
			const { path: title, size } = data
			const isSize = isString(size)
			const range = isSize ? 0 : parseFloat(size[3].substring(0, size[3].lastIndexOf('%')))
			const desc = isSize ? `未挂载` : `${size[5]}/${size[0].replace(' ', '')}`
			diskList.push({
				show: false,
				data,
				info: { title: isSize ? '未挂载' : title, range, desc },
			})
			quota.push({
				diskName: title,
				diskQuota: Number.parseFloat(size[0].split('G')[0]),
			})
		})
		diskUsage.value = list[0]?.size || [] // 磁盘使用情况，仅根目录
		disk.value = diskList
		diskQuota.value = quota
		localStorage.setItem('checkWarnInfo', JSON.stringify(diskList[0])) // 磁盘信息
	}

	/**
	 * @description 监听登录信息
	 */
	const monitorLoginInfo = async () => {
		const info = sessionStorage.getItem('loginInfo') as string | null
		if (info !== undefined && info !== null) {
			let loginInfo = JSON.parse(info)
			// 登录成功提示 有上次登录信息才提示
			if (loginInfo != undefined && loginInfo != '' && loginInfo?.last_login?.login_time_str) {
				ElNotification({
					title: '登录成功',
					message: h(LoginPrompt as any),
					type: 'success',
					position: 'bottom-right',
					dangerouslyUseHTMLString: true,
					customClass: 'login-success',
					onClose: () => sessionStorage.removeItem('loginInfo'),
				})
			}
		}
	}

	/**
	 * @description 获取首页负载等弹出框数据信息
	 */
	const getHomeProcessInfo = async () => {
		try {
			const {
				data: { info: cpuBaseInfo, CPU_high_occupancy_software_list: cpuProcess, memory_high_occupancy_software_list: memProcess },
			} = await getHomeResData()
			Object.assign(specificResource.value, {
				cpuBaseInfo,
				cpuProcess,
				memProcess,
				refresh: Date.now(),
			})
		} catch (error) {
			console.log(error)
		}
	}

	const $reset = () => {
		disk.value = []
		diskQuota.value = []
		recommendSoft.value = []
		network.value = []
		diskIo.value = []
		overviewShowList.value = []
		Object.assign(specificResource.value, {
			cpuProcess: [],
			memProcess: [],
		})
	}

	return {
		panelInfo,
		memory,
		systemInfo,
		refreshTime,
		load,
		cpu,
		cpuTimes,
		disk,
		diskQuota,
		recommendSoft,
		network,
		diskIo,
		diskUsage,
		overviewShowList,
		isRefreshData,
		recommendInstall,
		specificResource,
		recommendInstallDialog,
		getUpdateInfo,
		getUpdateInfoNew,
		getHomeConfig,
		getSystemInfo,
		monitorLoginInfo,
		getHomeProcessInfo,
		$reset,
	}
})

export default HOME_STORE
