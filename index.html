<!doctype html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<meta content="IE=edge" />
		<meta name="referer" content="never" />
		<meta name="referer" content="no-referrer" />
		<meta name="renderer" content="webkit" />
		<!-- <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;" name="viewport" /> -->
		<title><%- title %></title>
		<link rel="shortcut icon" href="<%- logo %>favicon.ico" type="image/x-icon" />
		<script type="text/javascript">
			this.globalThis || (this.globalThis = this)
			;(function () {
				window.vite_public_title = '<%- title %>' // 网站标题
				window.vite_public_menu = '<%- menu %>' // 菜单栏目
				window.vite_public_debug = '<%- debug %>' // 是否开启调试模式
				window.vite_public_pyversion = '<%- pyVersion %>' // python版本
				window.vite_public_version = '<%- panelVersion %>' // 当前本地版本号
				window.vite_public_ip = '<%- panelIp %>' // 当前服务器IP地址
				window.vite_public_brand = '<%- brand %>' // 品牌
				window.vite_public_product = '<%- product %>' // 产品
				window.vite_public_year = '<%- year %>' // 年份
				window.vite_public_cdn_url = '<%- cdnUrl %>' // cdn地址
				window.vite_public_request_token = '<%- requestToken %>' // csrf_token
				window.vite_public_soft_flush = '<%- flushSoft %>' // 是否刷新软件列表，解决缓存问题
				window.vite_public_encryption = '<%- encryptionCode %>' // rsa加密公钥
				window.vite_public_proxy_key = '<%- proxyKey %>' // 代理服务信息
				window.vite_public_proxy_ip = '<%- proxyIp %>' // 代理服务信息
				window.vite_public_proxy_port = '<%- proxyPort %>' // 代理服务信息
				window.vite_public_panel_type = '<%- otherJs %>' // 面板类型
				window.vite_public_file_version = '%VERSION-NUMBER%' // 当前版本随机数，用于编译替换

				// 禁止后退
				function blockHist() {
					if (window.history && window.history.pushState) {
						window.onpopstate = function () {
							window.history.pushState('forward', null, '')
							window.history.forward(1)
						}
					}
					window.history.pushState('forward', null, '') //在IE中必须得有这两行
					window.history.forward(1)
				}
				// 跳转处理
				blockHist()
			})()
		</script>
	</head>

	<body>
		<div id="root"></div>
	</body>
</html>
