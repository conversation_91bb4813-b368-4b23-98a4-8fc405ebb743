<!doctype html>
<html lang="cn">
	<head>
		<meta charset="utf-8" />
		<meta content="IE=edge" />
		<meta name="referer" content="never" />
		<meta name="renderer" content="webkit" />
		<title><%- title %></title>
		<link rel="shortcut icon" href="<%- logo %>favicon.ico" type="image/x-icon" />
		<%- loginStyle %>
		<script type="text/javascript">
			window.vite_public_title = '<%= title %>' // 网站标题
			window.vite_public_login_token = '<%= loginToken %>' // 登录token
			window.vite_public_encryption = '<%= encryptionCode %>' // rsa加密公钥
			window.vite_public_login_check = '<%= loginCheck %>' === 'True' // 是否登录检查
			window.vite_public_hosts_list = JSON.parse('<%= hostsList %>'.replace(/&#34;/g, '"')) // cdn主机列表
			;(function () {
				// 浏览器兼容性检测
				function browserCompatible() {
					// IE浏览器版本检测
					var ieVersion = (function () {
						var userAgent = navigator.userAgent,
							isLessIE11 = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1,
							isEdge = userAgent.indexOf('Edge') > -1 && !isLessIE11,
							isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1
						if (isLessIE11) {
							var IEReg = new RegExp('MSIE (\\d+\\.\\d+);')
							IEReg.test(userAgent)
							var IEVersionNum = parseFloat(RegExp['$1'])
							if ([7, 8, 9, 10].indexOf(IEVersionNum) > -1) {
								return IEVersionNum
							} else {
								return 6
							}
						} else if (isEdge) {
							return 'edge'
						} else if (isIE11) {
							return 11
						} else {
							return -1
						}
					})()
					if ((ieVersion != -1 && ieVersion <= 11) || ieVersion === 'edge') location.href = '/tips'

					// 支持Firefox\Chrome\Safari浏览器判断，排除移动设备
					var browserVersion = function (config) {
						var userAgent = navigator.userAgent
						// 判断是否为移动端设备
						if (userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)) return false
						if (type === 'Safari') type = 'Version'
						for (var i = 0; i < config.length; i++) {
							var type = config[i][0]
							var version = config[i][1]
							if (userAgent.indexOf(type) > -1) {
								var reg = new RegExp(type + '/(\\d+\\.\\d+)')
								reg.test(userAgent)
								var versionNum = parseFloat(RegExp['$1'])
								return version > versionNum
							} else {
								return false
							}
						}
					}
					if (
						browserVersion([
							['Chrome', 60],
							['Safari', 11],
							['Firefox', 55],
						])
					) {
						location.href = '/tips'
					}
					if (!location.origin) location.origin = location.protocol + '//' + location.hostname + (location.port ? ':' + location.port : '')
				}

				// 禁止后退
				function blockHist() {
					if (window.history && window.history.pushState) {
						window.onpopstate = function () {
							window.history.pushState('forward', null, '')
							window.history.forward(1)
						}
					}
					window.history.pushState('forward', null, '') //在IE中必须得有这两行
					window.history.forward(1)
				}

				// 主题切换的方法
				// function useDark(config) {
				// 	let theme = localStorage.getItem(config.storageKey) // 从本地存储中获取主题
				// 	function isDarkMode() {
				// 		const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
				// 		return darkModeQuery.matches
				// 	}
				// 	if (theme && theme.includes('auto') && isDarkMode) theme = isDarkMode ? config.valueDark : config.valueLight // 自动模式下，根据系统主题切换
				// 	document.querySelector(config.selector).setAttribute('class', theme) // 设置主题，使用setAttribute方法

				// 	// 设置主题，使用innerHTML方法
				// 	function setThemeDom(el) {
				// 		const divHtml = '<div class="cut-theme"><i class="svgtofont-icon-dark"></i></div>'
				// 		const divView = document.createElement('div')
				// 		divView.classList.add('theme-view') // 创建一个div元素
				// 		divView.innerHTML = divHtml // 设置div元素的innerHTML属性
				// 		console.log(divView)
				// 		if (document.getElementsByTagName('body')[0]) document.getElementsByTagName('body')[0].append(divView) // 将div元素添加到body元素中
				// 	}
				// 	setThemeDom('body')
				// }

				browserCompatible()
				blockHist()
				// useDark({
				// 	selector: 'html',
				// 	storageKey: 'PANEL-THEME',
				// 	valueDark: 'dark',
				// 	valueLight: 'light',
				// })
			})()
		</script>
	</head>
	<body>
		<script type="text/javascript" src="/static/js/qrcode.min.js"></script>
		<script type="text/javascript" src="/static/js/jsencrypt.min.js"></script>
		<script type="text/javascript" src="/static/js/md5.js"></script>
		<div id="app"></div>
		<!-- inject:js --><!-- endinject -->
	</body>
</html>
