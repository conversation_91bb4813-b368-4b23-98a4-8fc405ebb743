<template>
	<section id="layout-main" :class="'w-full p-[8px]'" :style="{ 'min-height': mainHeight + 'px' }">
		<router-view v-slot="{ Component }">
			<transition class="animate-animated" name="fade" mode="out-in">
				<component :is="Component" />
			</transition>
		</router-view>
		<BtFps />
	</section>
</template>

<script lang="ts" setup>
import { useGlobalStore } from '@/store/global'
import { useFtps } from '@/layout/controller'
const { mainHeight } = useGlobalStore() // 持久化状态
// const { BtFps } = useFtps()
</script>

<style lang="css" scoped>
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.1s;
}

.fade-enter,
.fade-leave-to {
	opacity: 0;
}
</style>
