import type { RouteRecordRaw } from 'vue-router'
import { useSettingsStore } from '@vhost/views/settings/useStore'
import { Message } from '@/hooks/tools'

// 页面路由拦截
// eslint-disable-next-line consistent-return
const beforeEnter: RouteRecordRaw['beforeEnter'] = async (to, from, next) => {
	try {
		const { useGlobalStore } = await import('@/store/global')
		const { payment, getAuthState } = useGlobalStore()
		const { authType } = toRefs(payment.value)
		const isVhostInstall = to.path.includes('/vhost/install') // 跳转到安装页面

		// 授权验证
		await getAuthState()
		if (authType.value.includes('free') && !isVhostInstall) {
			return next('/vhost/install')
		}

		// 安装检测(nginx和多机服务)
		const { getInfo, isInstall, runStatus } = useSettingsStore()
		await getInfo()
		console.log(!isInstall.value, runStatus.value, isVhostInstall)

		// 如果访问install页面但已经安装了，跳转到主页面
		if (isInstall.value && isVhostInstall) {
			return next('/vhost/settings')
		}

		if (!isInstall.value && !isVhostInstall) {
			return next('/vhost/install')
		}

		// 服务未运行
		if (isInstall.value && !runStatus.value && !to.path.includes('/vhost/settings')) {
			Message.warn('当前服务未运行，请先启动服务')
			return next('/vhost/settings')
		}

		next()
	} catch (error) {
		console.error(error)
	}
	next()
}

const vhostRoutes: RouteRecordRaw = {
	name: 'vhost',
	path: '/vhost',
	meta: { sort: 13, icon: 'vhost', title: '多机管理' },
	component: () => import('@vhost/index.vue'),
	children: [
		{
			path: 'account',
			name: 'vhost-account',
			meta: { title: '用户' },
			beforeEnter,
			component: () => import('@vhost/views/account/index.vue'),
		},
		{
			path: 'package',
			name: 'vhost-package',
			meta: { title: '资源' },
			beforeEnter,
			component: () => import('@vhost/views/package/index.vue'),
		},
		{
			path: 'storage',
			name: 'vhost-storage',
			meta: { title: '存储' },
			beforeEnter,
			component: () => import('@vhost/views/storage/index.vue'),
		},
		{
			path: 'logs',
			name: 'vhost-logs',
			meta: { title: '日志' },
			beforeEnter,
			component: () => import('@vhost/views/logs/index.vue'),
		},
		{
			path: 'settings',
			name: 'vhost-settings',
			meta: { title: '设置' },
			beforeEnter,
			component: () => import('@vhost/views/settings/index.vue'),
		},
		{
			path: 'install',
			name: 'vhost-install',
			meta: { title: '多用户安装', ignore: true },
			beforeEnter,
			component: () => import('@vhost/views/install/index.vue'),
		},
	],
}

export default vhostRoutes
