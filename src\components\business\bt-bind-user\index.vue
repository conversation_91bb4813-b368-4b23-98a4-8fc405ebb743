<template>
	<BtForm ref="bindUserRef" size="default" :rules="rules" :model="userInfo" class="bind-user-info"
		:class="{ 'bind-form': compData?.title ? true : false }">
		<BtFormItem class="!mb-5 flex !justify-center">
			<div class="flex !justify-center !w-full items-center">
				<h3 class="text-[2rem] text-dark inline-block mr-2 font-bold my-[10px]">
					{{ compData?.title || '切换宝塔官网账号' }}
				</h3>
				<BtPopover placement="top-start" width="200" trigger="hover"
					content="宝塔面板许多功能都依赖于官网，绑定仅限于为您提供更好的面板服务体验，不涉及您服务器任何敏感信息，请放心使用。"
					class="inline-block relative -top-1.5">
					<template #reference>
						<a class="bt-ico-ask align-middle inline-block" href="javascript:;"> ? </a>
					</template>
				</BtPopover>
			</div>
		</BtFormItem>

		<BtFormItem :key="1" prop="username" :class="{ '!mb-[3rem]': compData?.title ? true : false }">
			<BtInput v-model="userInfo.username" v-focus clearable required name="username" placeholder="宝塔官网账号"
				class="h-[4rem] leading-[4rem] w-full" :disabled="formDisabled.username"
				@keyup.enter.native="bindBtUser()" />
			<span v-if="compData?.title && showError" class="absolute el-form-item__error">
				<span>请使用宝塔官网登录账号进行登录，若未有账号，可进行</span>
				<BtLink class="!text-[12px]" href="https://www.bt.cn/register"> 免费注册 </BtLink>
			</span>
		</BtFormItem>
		<BtFormItem :key="2" prop="password">
			<BtInput v-model="userInfo.password" clearable required type="password" name="password" placeholder="密码"
				class="h-[4rem] leading-[4rem] w-full" :show-password="true" :disabled="formDisabled.password"
				@keyup.enter.native="bindBtUser()"></BtInput>
		</BtFormItem>

		<BtFormItem v-show="isCheck" prop="code">
			<BtInput v-model="userInfo.code" clearable placeholder="验证码" type="text" @keyup.enter.native="bindBtUser()">
				<template #append>
					<BtButton class="relative top-0 rounded-none rounded-r-[3px] w-full !h-[auto]" size="large"
						type="primary" :disabled="formDisabled.code" plain @click="getVerifyCode">
						{{ tipsText }}
					</BtButton>
				</template>
			</BtInput>
		</BtFormItem>
		<BtButton type="primary" class="w-[100%] mt-[1.2rem]" size="large" @click="bindBtUser()">
			{{ compData?.btn || (compData?.title ? '绑定账号' : '切换账号') }}
		</BtButton>
		<div class="flex justify-end items-center mt-8 text-[1.2rem]">
			<BtLink href="https://www.bt.cn/register">注册账号</BtLink>
			<BtDivider />
			<BtLink href="https://www.bt.cn/login.html?page=reset">忘记密码</BtLink>
			<BtDivider />
			<BtLink href="https://www.bt.cn/bbs">问题反馈</BtLink>
		</div>
	</BtForm>
</template>

<script lang="ts" setup>
import { getBindCode } from '@/api/global'
import { useGlobalStore } from '@/store/global'
import { inputFocus } from '@/utils'
import { useMessage } from '@hooks/tools'
import { bindUserRequest } from './useController'

const { getGlobalInfo } = useGlobalStore()

const message = useMessage()

interface Props {
	compData?: {
		title: string
		btn: string
	}
}

const props = withDefaults(defineProps<Props>(), {
	// eslint-disable-next-line vue/require-valid-default-prop
	compData: () => ({
		title: '',
		btn: '',
	}),
})

// 是否验证码验证
const isCheck = ref(false)
// 获取验证码按钮文字
const tipsText = ref('获取验证码')
// 用户登录数据
const userInfo = reactive({ username: '', password: '', code: '' })
// 表单状态配置
const formDisabled = reactive({
	username: false,
	password: false,
	code: false,
})
// 用户登录Token
const userToken = ref('')
// 定时器状态
const clearIntervalVal = ref()
// 表单实例
const bindUserRef = ref()
// 是否显示错误信息
const showError = ref(false) // 是否显示错误信息
// 表单规则
const rules = ref({
	username: [
		{
			validator: (rule: any, value: any, callback: any) => {
				if (props.compData?.title) showError.value = false
				if (/^1[3456789]\d{9}$/.test(value)) {
					callback()
				} else if (value === '') {
					callback(new Error('请输入宝塔官网账号'))
				} else {
					if (props.compData?.title) {
						showError.value = true
						callback(new Error(' '))
						return
					}
					callback(new Error('请输入正确的手机号'))
				}
			},
			trigger: ['blur', 'change'],
		},
	],
	password: [
		{ required: true, message: '请输入密码', trigger: ['blur', 'change'] },
		{
			validator: (rule: any, value: any, callback: any) => {
				if (value.length >= 8) {
					callback()
				} else {
					callback(new Error('请输入至少8位密码'))
				}
			},
			trigger: ['blur', 'change'],
		},
	],
	token: [
		{ required: false, message: '请输入验证码', trigger: ['blur', 'change'] },
		{
			validator: (rule: any, value: any, callback: any) => {
				if (!isCheck.value) callback()
				if (value.length === 6) {
					callback()
				} else {
					callback(new Error('请输入6位验证码，不可为空'))
				}
			},
			trigger: ['blur', 'change'],
		},
	],
})

/**
 * @description 绑定宝塔官网账号
 * @returns
 */
const bindBtUser = () => {
	bindUserRef.value.validate(async (valid: boolean) => {
		if (valid) bindUserRequest({ isCheck, userInfo, userToken })
	})
}

/**
 * @description 倒计时
 * @param {number} time 倒计时时间
 */
const countDown = (time: number) => {
	if (clearIntervalVal.value) clearInterval(clearIntervalVal.value)
	const intervalFun = () => {
		time--
		if (time <= 0) {
			clearInterval(clearIntervalVal.value)
			tipsText.value = '获取验证码'
			formDisabled.code = false
			return
		}
		tipsText.value = `重新获取验证码(${time}s)`
	}
	intervalFun()
	clearIntervalVal.value = setInterval(intervalFun, 1000)
	formDisabled.code = true
}

/**
 * @description 获取验证码
 * @returns {Promise<void>}
 */
const getVerifyCode = async (): Promise<void> => {
	if (!userInfo.username || !userInfo.password) {
		message.error('请先输入手机号和密码')
		return
	}
	formDisabled.username = true
	formDisabled.password = true
	countDown(60) // 设置倒计时
	const rdata = await getBindCode({
		username: userInfo.username,
		token: userToken.value,
	})
	message.request(rdata)
}

/**
 * @description 打开弹窗
 */
const onOpen = () => {
	inputFocus(bindUserRef.value.$el, 'username')
}

/**
 * @description 关闭弹窗
 */
const onCancel = () => {
	bindUserRef.value.clearValidate()
}

defineExpose({
	onOpen,
	onCancel,
})

onMounted(() => {
	bindUserRef.value?.clearValidate()
	getGlobalInfo()
})
</script>

<style lang="css" scoped>
.bind-user-info {
	padding: 3rem;
}

.bind-user-info :deep(.el-form .el-form-item--small.el-form-item + .el-form-item) {
	margin-top: 2.4rem !important;
}

.bind-form :deep(.el-input__inner) {
	height: 3rem;
	line-height: 4rem;
}

:deep(.el-form .el-form-item__content) {
	justify-content: center !important;
}
</style>
