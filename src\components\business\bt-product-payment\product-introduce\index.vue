<template>
	<div class="sidebar-info" :class="`sidebar-${tab?.activeTypeInfo?.type}`">
		<div class="sidebar-title">
			<span v-show="!isPlugin" :class="'icon-currency !mr-10px !w-[2.4rem] icon-' + tab?.activeTypeInfo?.type"></span>
			<span>{{ tab?.activeTypeInfo?.title }}</span>
		</div>
		<div v-show="!isPlugin" class="sidebar-describe">{{ tab?.activeTypeInfo?.describe }}</div>
		<div class="flex flex-col">
			<div class="sidebar-privilege">{{ tab?.activeTypeInfo?.tipsTitle }}:</div>
			<div v-if="tab?.activeTypeInfo?.type === 'plugin'" class="w-[16.5rem] text-[1.4rem] leading-loose mt-[2rem]" v-html="compData?.pluginInfo?.ps"></div>
			<div v-else class="pt-16px">
				<div v-for="(item, index) in tab?.activeTypeInfo?.tipsList" :key="index" class="sidebar-tips-item">
					<span class="mr-8px"><i class="svgtofont-el-check font-black text-[#FEAA04] text-[1.6rem]"></i></span>
					<span class="whitespace-nowrap">{{ item }}</span>
				</div>
			</div>
		</div>
		<div v-if="tab?.activeTypeInfo?.type === 'dev'" class="sidebar-tips-item pro-introduce safe-report">
			<span data-v-0c1f7542="" class="mr-4px"><i class="svgtofont-el-star-filled font-black text-[#FEAA04]"></i></span><span><a target="_blank" href="https://www.bt.cn/new/product/safety_report.html" style="text-decoration: underline">获取安全检查报告</a></span>
		</div>
		<div v-if="tab?.activeTypeInfo?.type === 'plugin'" class="pro-left-footer-recom">
			<span>Linux企业版可免费使用【{{ tab?.activeTypeInfo?.title }}】等30+款插件，价格仅售￥<span class="plugin_pro_price">1399</span>/年</span>
		</div>
		<div v-else-if="tab?.activeTypeInfo?.type !== 'coupon'" class="sidebar-details" @click="openPrivilegeContrast">特权对比</div>
		<a v-else class="sidebar-details" href="https://www.bt.cn/new/activity.html" target="_blank"> 查看更多活动 </a>
	</div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import PRODUCT_PAYMENT_STORE from '../store'

const store = PRODUCT_PAYMENT_STORE()
const { isRemarksLoading, productInfo: tab, isPlugin, compData } = storeToRefs(store)
const { changeTypeTabEvent, openPrivilegeContrast } = store
</script>

<style lang="css" scoped>
/* 侧边栏 */
.sidebar-info {
	@apply flex-[2] bg-[#efefef] rounded-tl-4px rounded-bl-4px pt-[2.8rem] pl-[2.4rem];
}
.sidebar-title {
	@apply pb-16px flex text-center pt-24px font-bold text-[2rem] items-center;
}
.sidebar-describe {
	@apply mb-20px text-[1.2rem] h-[4rem] pr-16px leading-6 text-light;
}
.sidebar-tips-item {
	@apply py-[6px] text-[1.4rem];
}
.sidebar-privilege {
	-webkit-background-clip: text !important;
	color: transparent !important;
	@apply mb-8px text-[1.6rem] text-medium;
}
.sidebar-details {
	-webkit-background-clip: text !important;
	color: transparent !important;
	@apply w-[14rem] h-[4rem] absolute bottom-18 left-16 leading-[3.8rem] block text-center text-[1.6rem] text-white rounded-4px cursor-pointer;
}

/* 侧边栏产品类型专业版 */
.sidebar-pro,
.sidebar-plugin {
	@apply bg-[#FFF5E1] bg-no-repeat;
	background-image: url('/static/payment/lib-pay-pro-bg-logo.png');
	background-position: 2rem 58rem;
}
.sidebar-pro .sidebar-privilege,
.sidebar-plugin .sidebar-privilege {
	color: #666 !important;
}
.sidebar-pro .sidebar-title span:last-child,
.sidebar-plugin .sidebar-title span:last-child {
	background-image: linear-gradient(to right, #e8a833, #b48a52);
}
.sidebar-pro .sidebar-describe,
.sidebar-plugin .sidebar-describe,
.sidebar-pro .sidebar-tips-item,
.sidebar-plugin .sidebar-tips-item {
	@apply text-[#666];
}
.sidebar-pro .sidebar-details,
.sidebar-plugin .sidebar-details {
	border: 2px solid #ffb95a;
	background: linear-gradient(to bottom, #e8aa36, #e9c571, #af7323);
}
.pro-left-footer-recom {
	position: absolute;
	bottom: 30px;
	border: #d8ae69 1px solid;
	background: #fff9ef;
	border-radius: 5px;
	margin-right: 20px;
	padding: 40px 12px;
	font-size: 14px;
	line-height: 25px;
	font-weight: bold;
	width: 18rem;
}
.pro-left-footer-recom:before {
	content: '购买推荐';
	background: url('/static/payment/lib-pay-left-footer-plugin-explain.png');
	width: 93px;
	height: 23px;
	position: absolute;
	right: -1px;
	top: -8px;
	background-size: 100%;
	color: #fff;
	font-size: 15px;
	text-align: right;
	padding: 0px 10px;
	font-weight: initial;
}
.pro-left-footer-recom span {
	background-image: linear-gradient(to right, #e8a833, #b48a52);
	-webkit-background-clip: text;
	color: transparent;
}

/* 侧边栏抵扣券 */
.sidebar-coupon {
	@apply bg-[#FFF5E1];
	background-image: none;
}
.sidebar-coupon .sidebar-privilege {
	color: #666 !important;
}
.sidebar-coupon .sidebar-title span:last-child {
	@apply text-[2.3rem];
	background-image: linear-gradient(to right, #e8a833, #b48a52);
}
.sidebar-coupon .sidebar-describe,
.sidebar-coupon .sidebar-tips-item {
	@apply text-[#666];
}
.sidebar-coupon i:before {
	content: '·';
	color: #666;
}
.sidebar-coupon .sidebar-details {
	color: #20a53a !important;
	border: 0.2rem solid #20a53a;
}

/* 侧边栏产品类型企业版、运维版 */
.sidebar-ltd,
.sidebar-dev {
	background-color: rgba(47, 52, 55, 0.95);
	background-image: url('/static/payment/lib-pay-ltd-bg-logo.png');
	background-repeat: no-repeat;
	background-position-y: bottom;
}
.sidebar-ltd .sidebar-title span:last-child,
.sidebar-dev .sidebar-title span:last-child,
.sidebar-ltd .sidebar-privilege,
.sidebar-dev .sidebar-privilege {
	background-image: linear-gradient(to bottom, #e8a833, #ebc874, #ae7222);
}
.sidebar-ltd .sidebar-describe,
.sidebar-dev .sidebar-describe,
.sidebar-ltd .sidebar-tips-item,
.sidebar-dev .sidebar-tips-item {
	@apply text-[#c7c7c7];
}
.sidebar-ltd .sidebar-details,
.sidebar-dev .sidebar-details {
	border: 2px solid #ffb95a;
	background: linear-gradient(to bottom, #e8aa36, #e9c571, #af7323);
}

.sidebar-info .icon-pro,
.icon-pro {
	background-image: url('/static/icons/icon-pro.svg');
}

.sidebar-info .icon-ltd,
.sidebar-info .icon-dev,
.icon-ltd,
.icon-dev {
	background-image: url('/static/icons/icon-ltd.svg');
}

.sidebar-title span:last-child,
.sidebar-privilege {
	-webkit-background-clip: text;
	color: transparent;
}

.icon-currency {
	@apply w-[2rem] h-[2rem] inline-block mr-4px bg-no-repeat bg-contain bg-center;
}
</style>
