<template>
	<div class="flex flex-col p-[10px]">
		<div class="flex justify-between mb-[14px]">
			<span>{{ bindUser }}</span>
			<span v-if="!forceLtd && !aliyunEcsLtd">
				<span class="user-operate" @click="openBindDialog">切换</span>
				<el-divider direction="vertical" />
				<span class="user-operate" @click="unbindUser">解绑</span>
			</span>
		</div>
		<div class="flex mb-[14px]">
			<el-card class="is-always-shadow !border-[#efefef]">
				<div v-if="authType !== 'pro'" class="user-ltd-info p-[1.4rem] h-[4.6rem] flex items-center justify-between">
					<div class="flex items-center">
						<span class="h-[2.4rem] w-[6rem] mt-[0.2rem] bg-left bg-no-repeat cursor-pointer" :class="ltdIcon" @click="store.productPayment"></span>
						<div :class="!authExpirationTime ? 'text-danger' : 'text-{#666}'" class="ml-[0.4rem] text-[1.2rem]">
							{{ ltdInfo }}
						</div>
					</div>
					<el-button class="payment-btn" type="primary" size="small" @click="store.productPayment">
						{{ ltdBtn }}
					</el-button>
				</div>
				<div v-else class="user-ltd-info p-[1rem] pl-[1.4rem] pr-[1.4rem] h-[7.2rem] mt-[-0.5rem]">
					<div class="flex items-center">
						<span class="h-[2.4rem] w-[6rem] mt-[0.2rem] bg-left bg-no-repeat cursor-pointer" :class="ltdIcon" @click="store.productPayment"></span>
						<div class="ml-[0.4rem] text-[1.2rem] text-{#666}">{{ ltdInfo }}</div>
					</div>
					<div class="flex justify-between items-end">
						<span class="ml-[0.4rem] leading-none">企业版享有以下特权</span>
						<el-button type="primary" size="small" @click="store.productPayment">{{ ltdBtn }}</el-button>
					</div>
				</div>
				<div class="product-card-content">
					<template v-for="(item, index) in advantageList" :key="index">
						<div v-if="item[0] !== 'other'" class="advantage-item">
							<i :class="`svgtofont-free-${item[0]}-icon text-[1.8rem]  mr-[4px] text-[#9e6700]`"></i>
							<span>{{ item[1] }}</span>
						</div>
						<!-- 其他特权-点击跳转 -->
						<bt-link v-if="item[0] === 'other'" class="advantage-item other" href="https://www.bt.cn/new/pricing.html">
							{{ item[1] }}
						</bt-link>
					</template>
				</div>
			</el-card>
		</div>
		<!-- 更多操作 -->
		<div class="flex flex-col">
			<el-popover width="172" :show-arrow="false" placement="right">
				<template #reference>
					<div class="menu-item">
						<span>{{ serviceName }}</span>
						<i v-show="authType === 'ltd'" class="svgtofont-free-customer ltd-customer-tips"></i>
					</div>
				</template>
				<div class="qrcode-wechat">
					<bt-link :href="ltdCustomerUrl" class="seek-link" target="_blank"> 点击咨询客服<i class="svgtofont-arrow text-[#20a53a]"></i> </bt-link>
					<div class="qrcode-content">
						<bt-qrcode :value="ltdCustomerUrl" :size="128" />
						<bt-image :all="true" :src="`/static/icons/we-com.svg`" class="!absolute bg-white border-[0.2rem] border-lightest rounded-[2px] mr-[4px] w-2.4rem h-2.4rem top-[50%] left-[50%] -ml-1.2rem -mt-1.2rem" />
					</div>
					<div class="wechat-title flex items-center justify-center">
						<i class="svgtofont-icon-scan text-[2.4rem]"></i>
						<div class="scan-title text-[1.6rem] ml-[1rem] text-[#666666] font-bold">扫一扫</div>
					</div>
					<bt-link v-if="authType === 'ltd'" class="survey-link" @click="npsSurveyLtdDialog"> 企业版用户专属调研 </bt-link>
				</div>
			</el-popover>
			<!-- <div class="menu-item !border-none" @click="doLogin">退出面板</div> -->
		</div>
	</div>
</template>

<script setup lang="ts">
import { unbindUser, doLogin, npsSurveyLtdDialog, bindUserDialog } from '@/public'
import { useGlobalStore } from '@store/global'
import { formatTime } from '@utils/index'
import HOME_HEADER_STORE from '@home/views/header/store'
import { storeToRefs } from 'pinia'

const store = HOME_HEADER_STORE()
const { authExpirationTime, authType, bindUser, advantageList } = storeToRefs(store)
const { forceLtd, aliyunEcsLtd } = useGlobalStore()

// 企业版图标
const ltdIcon = computed(() => {
	switch (authType.value) {
		case 'ltd':
			return 'icon-paid-ltd'
		case 'pro':
			return 'icon-paid-pro'
		default:
			return 'icon-unpaid-ltd'
	}
})

/**
 * @description 企业版图标 -- 计算属性
 */
const ltdBtn = computed(() => {
	switch (authType.value) {
		case 'ltd':
			return '续费'
		default:
			return '立即升级'
	}
})

// 打开绑定弹窗
const openBindDialog = async () => {
	bindUserDialog()
	// if (window.$extension) {
	// 	// 打开第三方绑定弹窗
	// 	const plugin = await window.$extension()
	// 	if (plugin.binduserPopup) {
	// 		plugin.binduserPopup()
	// 	}else{
	// 		// 绑定宝塔官网账号
	// 		bindUserDialog()
	// 	}
	// }else{
	// 	// 绑定宝塔官网账号
	// 	bindUserDialog()
	// }
}

/**
 * @description 企业版到期时间 -- 计算属性
 */
const ltdInfo = computed(() => {
	const formattedDate = formatTime(authExpirationTime.value, 'yyyy-MM-dd')
	switch (authType.value) {
		case 'ltd':
			return `${formattedDate}到期`
		case 'pro':
			if (authExpirationTime.value === 0) return '永久授权'
			return `${formattedDate}到期`
		default:
			return `免费版`
	}
})

/**
 * @description 企业版客服链接 -- 计算属性
 */
const ltdCustomerUrl = computed(() => {
	return `https://www.bt.cn/new/wechat_customer?${authType.value === 'ltd' ? `vip=` : ''}`
})

/**
 * @description 企业版客服链接 -- 计算属性
 */
const serviceName = computed(() => {
	if (authType.value === 'ltd') return '您的专属客服'
	return '帮助与客服'
})
</script>

<style lang="css" scoped>
:deep(.payment-btn.el-button--primary) {
	background: rgb(240, 173, 78) !important;
	border-color: rgb(240, 173, 78) !important;
	@apply h-[2.4rem] flex flex-row items-center justify-center;
}

.payment-btn.el-button--primary span {
	@apply text-[1.2rem];
}

.payment-btn.el-button--primary:hover {
	@apply bg-[#C6892E] border border-1 border-[#C6892E];
}

.user-operate:hover {
	@apply cursor-pointer text-primary;
}

.product-card-header {
	@apply flex p-[12px] border-b-[1px] border-solid border-[#EBEEF5];
	background: linear-gradient(140.75deg, rgba(255, 246, 241, 0.23) -48.385%, rgba(255, 244, 237, 0.23) -16.724%, rgba(255, 245, 238, 0.23) -5.651%, rgba(255, 206, 108, 0.45) 194.528%);
}

.product-card-content {
	@apply px-[12px] pt-[12px] text-[12px] flex flex-wrap;
	background: linear-gradient(140.75deg, rgba(255, 246, 241, 0.15) -55.417%, rgba(255, 244, 237, 0.15) -22.187%, rgba(255, 245, 238, 0.15) -10.565%, rgba(255, 206, 108, 0.3) 199.533%, transparent 20px);
}

:deep(.advantage-item) {
	@apply flex items-center text-[12px] text-[#999] w-[50%] mb-[10px];
}

.advantage-item.other:hover {
	@apply text-primary cursor-pointer;
}

.el-card {
	border: none;
	box-shadow: -2px 3px 4px rgba(217, 217, 217, 0.25);
	border-radius: 8px;
}

.user-ltd-info {
	border-bottom: 0.5px solid rgb(235, 238, 245);
	border-top-right-radius: 8px;
	border-top-left-radius: 8px;
	background: linear-gradient(140.75deg, rgba(255, 246, 241, 0.23) -48.385%, rgba(255, 244, 237, 0.23) -16.724%, rgba(255, 245, 238, 0.23) -5.651%, rgba(255, 206, 108, 0.45) 194.528%);
}

.user-ltd-intro {
	border-bottom-right-radius: 8px;
	border-bottom-left-radius: 8px;
	background: linear-gradient(140.75deg, rgba(255, 246, 241, 0.15) -55.417%, rgba(255, 244, 237, 0.15) -22.187%, rgba(255, 245, 238, 0.15) -10.565%, rgba(255, 206, 108, 0.3) 199.533%, transparent 20px);
}

.user-ltd-intro .other:hover {
	@apply cursor-pointer text-[#20a53a];
}

.el-divider--horizontal {
	margin: 0;
}

.menu-item {
	@apply p-[10px] border-t-[1px] border-dashed border-[#EBEEF5] flex items-center;
}

.menu-item:hover {
	@apply cursor-pointer text-primary bg-[#f5f7fa];
}

.ltd-customer-tips {
	@apply text-[7rem] h-[2rem] text-[#f0ad4e] flex justify-center items-center;
}

.qrcode-wechat {
	@apply flex flex-col justify-center mb-[8px];
}

:deep(.seek-link) {
	@apply flex justify-center items-center mb-[10px];
}

.qrcode-content {
	@apply flex justify-center mb-[10px];
}

:deep(.survey-link) {
	@apply text-[16px] mt-8px cursor-pointer text-center;
}
</style>
