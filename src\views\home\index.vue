<template>
	<section>
		<el-row :span="24">
			<el-col :span="24">
				<!--首页导航栏 -->
				<Header />
				<!-- 通知 -->
				<Notice />
				<!-- 状态 -->
				<State />
				<!-- 概览 -->
				<Overview />
			</el-col>
		</el-row>
		<el-row :gutter="12" :span="24">
			<el-col :md="12" :sm="24" :span="12" :xs="24">
				<!-- 软件 -->
				<Software class="h-[48rem] mb-0" />
			</el-col>
			<el-col :md="12" :sm="24" :span="12" :xs="24">
				<!-- 监控 -->
				<Monitor class="h-[48rem] mb-0" />
			</el-col>
		</el-row>
		<!-- 推荐企业版，移动至底部 -->
		<Recommend class="mt-[1.2rem]" />
	</section>
</template>

<script lang="ts" setup>
import { useGlobalStore } from '@store/global' // 全局持久化数据

import { useRequestCanceler } from '@hooks/tools/axios/model/axios-cancel' // 取消请求
import { delayExecAsync, pollingAsync } from '@/public' // 延迟执行异步方法
import { homeData } from '@home/useMethod' // Home数据

import Header from '@home/views/header/index.vue' // 导航栏
import Notice from '@home/views/notice/index.vue' // 通知
import State from '@home/views/state/index.vue' // 状态
import Overview from '@home/views/overview/index.vue' // 概览
import Software from '@home/views/software/index.vue' // 软件
import Monitor from '@home/views/monitor/index.vue' // 监控
import Recommend from '@home/views/recommend/index.vue' // 推荐
import HOME_STORE from '@home/store'
import { storeToRefs } from 'pinia'
import { useIdle } from '@/hooks/tools/idle'

const { getAlarmData, forceLtd, payment, panel, aliyunEcsLtd } = useGlobalStore() // 获取全局持久化数据

const store = HOME_STORE()
const { panelInfo } = storeToRefs(store)
const { isRestart } = toRefs(panelInfo.value) // 是否安装，是否重启

let idle: any = null

/**
 * @description 监听是否安装，默认触发一次检查
 */
watch(
	() => panel.value.initInstall,
	val => {
		if (!val) {
			// 强制安装企业版和是否购买企业版
			if ((forceLtd.value || aliyunEcsLtd.value) && payment.value.authType === 'free') return
			store.recommendInstallDialog()
		}
	},
	{ immediate: true, deep: true }
)

onMounted(async () => {
	store.monitorLoginInfo() // 登录信息
	// 当面板重启时，不再进行轮询
	// pollingAsync(async () => {
	// 	await store.getSystemInfo()
	// 	return isRestart.value
	// }, 3000)
	idle = useIdle({
		idle: 3600000,
		request: async () => {
			await store.getSystemInfo()
			return isRestart.value
		},
		timeout: 3000,
		retries: 3,
	})

	// 获取全局配置-首页
	store.getHomeConfig()
	// 告警数据, 1s延迟，当前方法会判断路由是否切换，如果切换则不执行
	// store.getUpdateInfo,
	delayExecAsync({
		promises: [getAlarmData, store.getUpdateInfoNew], // 获取告警数据 & 获取更新信息
		delay: 1000,
	})
})

// 离开首页
onUnmounted(() => {
	idle?.stop()
	// homeData.unload();

	// 取消初始化请求
	useRequestCanceler(['/system?action=GetNetWork', '/panel/overview/GetOverview', '/plugin?action=get_index_list', '/ajax?action=get_pay_type', '/mod/push/task/get_task_list', '/auth?action=get_coupon_list'])
})
</script>
