<template>
	<div v-loading="isLoading" :class="(isUpgrade ? 'update-back ' : '') + 'flex relative w-[100%] flex-col h-[100%] rounded-[.2rem] update-version-view'">
		<!-- 更新title -->
		<template v-if="isUpgrade">
			<div class="w-[150px] fixed right-30px" :style="`top:${mainHeight / 2 - 350}px;right:${mainWidth / 2 - 81}px`">
				<bt-image src="/update/update_rocket.svg" alt="rocket img" />
			</div>
			<div class="absolute top-15px left-15px text-white text-[1.4rem]">
				<span>{{ `${os}${versionType}本更新` }}</span>
			</div>
		</template>
		<!-- 版本title -->
		<template v-else>
			<div class="w-full absolute h-140px absolute-image rounded-[2px] title-new-img"></div>
			<div class="absolute top-15px left-15px text-white text-[1.4rem]">
				<span>宝塔{{ os }}面板</span>
			</div>
		</template>

		<!-- 更新视图 -->
		<div :class="isUpgrade ? 'relative mt-52 rounded-[2px]' : 'relative mt-25 bg-linear rounded-[2px]'" v-bt-loading="loading" v-bt-loading:title="'正在加载更新信息，请稍后...'">
			<!-- 需要更新 -->
			<template v-if="isUpgrade">
				<div class="text-[2.1rem] flex font-bold items-center justify-center text-[#565656]">
					<span>发现新的版本！</span>
				</div>
				<div class="text-[1.5rem] mt-8 flex justify-between mb-16px mx-34px update-text">
					<div class="">
						<span>最新版本:&nbsp;</span>
						<span>
							<bt-link href="https://www.bt.cn/bbs/forum-36-1.html">{{ os }}-{{ currentInfo?.version }} </bt-link>
						</span>
						<span v-if="grayscale < 1" class="text-primary">
							<el-tooltip class="item" effect="dark" content="提示：当前是正式版尝鲜阶段，为了验证新版本的稳定性和兼容性，确保全面推出之前，新功能能正常运行。" placement="top">
								<span>（尝鲜版<i class="svgtofont-el-question-filled"></i>）</span>
							</el-tooltip>
						</span>
					</div>
					<div class="">
						<span>更新时间：{{ currentInfo?.uptime }}</span>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="text-[2.1rem] flex mt-3rem pb-8 font-bold items-center justify-center text-[#565656] bg-linear">
					<bt-image src="/update/update_icon.svg" alt="icon" class="h-12 mr-2 mt-1" />
					<span class="leading-10">当前已经是最新版本</span>
				</div>
				<div class="px-32px py-[1.6rem] text-[1.4rem] flex items-center justify-between update-text bg-[white]">
					<span class="flex items-center">
						当前版本：
						<bt-link class="bt-link text-[12px]" href="https://www.bt.cn/bbs/forum-36-1.html" v-text="os + versionType + panelVersion" />
						<span v-if="grayscale < 1" class="text-primary">
							<el-tooltip class="item" effect="dark" content="提示：当前是正式版尝鲜阶段，为了验证新版本的稳定性和兼容性，确保全面推出之前，新功能能正常运行。" placement="top">
								<span>（尝鲜版<i class="svgtofont-el-question-filled"></i>）</span>
							</el-tooltip>
						</span>
					</span>
					<span> 发布时间：{{ currentInfo?.uptime }} </span>
				</div>
			</template>
			<!-- 中部 -->
			<div :class="isUpgrade ? 'mb-[3rem] mt-4 px-16px py-22px mx-34px bg-[rgba(33,165,59,0.04)] rounded-[2px]' : 'mb-[2.2rem] mt-2 px-16px py-14px mx-32px border-1 border-[#EEEFEC] bg-[rgb(247,252,248)] rounded-[2px]'">
				<!-- 更新日志内容 -->
				<div v-if="isUpgrade" class="rounded-md update-content">
					<div class="leading-10 mr-2 text-[1.2rem] text-medium overflow-auto" v-html="currentInfo?.updateMsg || '获取中'"></div>
				</div>
				<hr v-if="isUpgrade" />
				<!-- 提示信息 -->
				<div class="flex flex-col justify-center leading-10 text-[1.2rem] text-medium">
					<!-- <span v-if="isBeta">
						如需切换回正式版请点击
						<bt-link class="ml-2" @click="cutUpdateType">切换至正式版</bt-link>
					</span>
					<span v-if="!isBeta">
						如需切换测试版请点击
						<bt-link class="ml-2" @click="cutUpdateType">申请测试版</bt-link>
					</span> -->
					<div class="mt-0.6rem flex items-center">
						<span class="mr-2">有更新时提醒我:</span>
						<el-switch class="mr-4" v-model="update.updatePush" @change="store.openUpdateReminder" active-color="#13ce66" />
						<span class="bt-link" @click="store.updateReminderDialog(update.updatePush)" v-text="'提醒方式'" />
					</div>
				</div>
			</div>
			<div v-if="!isUpgrade">
				<div class="nps_survey_banner flex items-center justify-center" :class="isNPSShow ? '' : 'mb-[2rem]'">
					<span class="text-[14px] font-bold inline-block cursor-pointer text-[#20a53a] mr-[.4rem]" @click="isNPSShow = !isNPSShow" style="vertical-align: 4px"> 点击此处{{ isNPSShow ? '折叠' : '展开' }}需求反馈 </span>
					<i class="cursor-pointer text-[14px]" :class="isNPSShow ? 'svgtofont-el-arrow-up' : 'svgtofont-el-arrow-down'" @click="isNPSShow = !isNPSShow"></i>
				</div>
				<div class="pt-[1rem] pb-[3rem] pl-[3.1rem] pr-[3rem]" v-show="isNPSShow">
					<!-- 回答部分 -->
					<el-popover placement="top-start" width="400" popper-class="tips bg-[#20a53a] text-white text-[1.2rem] w-[40.6rem] !p-12px leading-[20px]" trigger="focus" content="如果您在使用过程中遇到任何问题或功能不完善，请将您的问题或需求详细描述给我们，我们将尽力为您解决或完善。">
						<template #reference>
							<el-input type="textarea" :rows="5" v-model="questions" placeholder="如果您在使用过程中遇到任何问题或功能不完善，请将您的问题或需求详细描述给我们，我们将尽力为您解决或完善。"></el-input>
						</template>
					</el-popover>
					<div class="mt-[1rem]">
						<div class="my-[1rem] flex items-center">
							<span class="text-[1.3rem] text-[#666]">是否愿意接受回访：</span>
							<el-switch size="small" v-model="form.status"></el-switch>
						</div>
						<el-form ref="formPhone" label-position="top" :model="form" :rules="rules">
							<el-form-item v-show="form.status" label="" prop="phone">
								<el-input v-model="form.phone" placeholder="请留下手机号码或邮箱"></el-input>
							</el-form-item>
						</el-form>
					</div>
					<div class="mt-[.4rem] text-[1.2rem] text-primary leading-[3rem]">我们特别重视您的需求反馈，我们会每周进行需求评审，希望能更好的帮到您</div>
					<div class="flex flex-col items-center p-[2rem]">
						<el-button type="primary" class="w-[12.5rem]" size="large" @click="store.submit(popupClose)">提交反馈</el-button>
					</div>
				</div>
			</div>

			<!-- 底部 -->
			<template v-if="isUpgrade">
				<div class="flex align-middle justify-center pb-10">
					<!-- 忽略版本 -->
					<el-button type="default" class="ignore-renew text-[1.3rem] border-none h-38px mr-50px w-50 bg-[#f0f0f0]" size="default" style="color: #999" round @click="store.ignoreUpdatEVersion(currentInfo.version, popupClose)">忽略本次更新</el-button>

					<!-- 立即更新 -->
					<el-button class="border-none text-[1.3rem] w-50 h-38px" type="primary" round @click="store.updateVersion()">立即更新</el-button>
				</div>
			</template>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { os } from '@utils/index'
import HOME_HEADER_STORE from '@home/views/header/store'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/global'
import HOME_STORE from '@/views/home/<USER>'
import { Message } from '@/hooks/tools'

const homeStore = HOME_STORE()
const { getUpdateInfoNew } = homeStore
const { panelInfo: info } = storeToRefs(homeStore)
const { upgrade, currentInfo } = toRefs(info.value)
const store = HOME_HEADER_STORE()
// isUpgrade
const { panelInfo, formPhone, loading, grayscale, panelVersion, betaInfo, officialInfo, isBeta, update, isNPSShow, questions, form, rules } = storeToRefs(store)

const isUpgrade = computed(() => upgrade.value !== 0) // 是否需要更新
// const currentInfo = computed(() => {
// 	return isBeta.value ? betaInfo.value : officialInfo.value
// }) // 当前版本信息

const { mainHeight, mainWidth } = useGlobalStore() // 主体高度

const versionType = computed(() => (panelInfo.value.isBeta ? '测试版' : '正式版')) // 版本类型
const popupClose = inject('popupClose', () => {}) // 关闭弹窗
const isLoading = ref(false) // 是否加载中
// /**
//  * @description 切换版本的更新方式
//  */
// const cutUpdateType = () => {
// 	if (!isBeta) {
// 		applyBetaDialog(updateVersion)
// 	} else {
// 		updateVersion(false) // 切换到正式版
// 	}
// }

const onOpen = async () => {
	if (!currentInfo.value) {
		isLoading.value = true
		const data = await getUpdateInfoNew() // 获取更新信息
		isLoading.value = false
		if (data?.msg) {
			Message.request(data)
			popupClose()
		}
	}
}

onMounted(async () => {
	await store.getUpdatePushInfo() // 获取更新提醒信息
})

onBeforeUnmount(() => {
	store.$reset()
})

defineExpose({ onOpen: onOpen })
</script>

<style lang="css" scoped>
.update-version-view :deep(.el-switch__core) {
	@apply bg-[#cdcdcd] border-[#e8eae9] border-1 border;
}
:deep(.el-switch.is-checked .el-switch__core) {
	@apply border border-[#20a53a] bg-[#20a53a];
}
.update-back {
	/* @apply bg-center bg-no-repeat; */
	background: url('/static/images/update/update_back1.svg') 95% center no-repeat;
	/* background-size: cover; */
	background-position-y: inherit;
}

hr {
	@apply w-[310px] mx-auto mb-[18px] border-0 border-t border-[#e3e3e3];
}

.update-content {
	@apply overflow-auto max-h-[18rem];
}

.update-content::-webkit-scrollbar {
	@apply w-[16px] h-[1px];
}

.update-content::-webkit-scrollbar-track {
	@apply bg-[#f1f1f1];
}

.update-content::-webkit-scrollbar-thumb {
	@apply bg-[#d6d7db] rounded-[50px];
	background-image: url('/static/images/scroll/scroll-bar-block.svg');
	background-position: bottom;
	background-repeat: no-repeat;
}

.update-content::-webkit-scrollbar-button:start {
	@apply bg-[#f1f1f1];
	background-image: url('/static/images/scroll/scroll-bar-up.svg');
	background-size: 100%;
	background-position: center;
	background-repeat: no-repeat;
	height: 15px;
}

.update-content::-webkit-scrollbar-button:end {
	@apply bg-[#f1f1f1];
	background-image: url('/static/images/scroll/scroll-bar-down.svg');
	background-size: 90%;
	background-position: center;
	height: 15px;
}

.bg-linear {
	@apply bg-gradient-to-t from-white to-transparent;
}

.absolute-image .el-image {
	@apply absolute;
}

.title-new-img {
	background: url('/static/images/update/update_back2_new.svg');
	background-repeat: no-repeat;
	background-size: contain;
	background-position: top;
}

.ignore-renew:hover {
	@apply bg-[#d4d4d4];
}
</style>
