<template>
	<section class="sidebar">
		<div v-if="route.path.includes('bind')" class="sidebar-mask"></div>
		<!-- 消息盒子和IP信息 -->
		<div class="flex-shrink-0">
			<BtContainer class="layout-message">
				<!-- <BtSvgIcon class="ml-[12px] mr-[4px] w-full" size="20" name="logo-white" /> -->
				<img src="/static/icons/logo-white.svg" alt="logo-white" :class="'w-[20px] ' + (menuControl.collapse ? 'ml-[8px]' : 'mr-[4px]')" />
				<!-- IP地址 -->
				<div v-if="!menuControl.collapse" class="w-[100px] leading-normal truncate" title="双击可复制IP" :class="messageIpNumberLength >= 11 ? 'text-[12px]' : 'text-[13px]'" @dblclick="copyIpAddressEvent">
					{{ panel.sidebarTitle || serverIp }}
				</div>

				<!-- 消息数 -->
				<div :class="messageNumberStyle" @click="openMessageBoxEvent">
					{{ panel.msgBoxTaskCount }}
				</div>
			</BtContainer>
		</div>
		<!-- 菜单列表 -->
		<div class="flex-1 overflow-y-auto overflow-hidden">
			<div class="relative h-full">
				<!--  进度条 -->
				<BtScrollbar>
					<ul class="bt-menu" :class="{ 'is-cut-menu': menuControl.collapse }">
						<!-- 渲染路由菜单 -->
						<template v-for="item in menuRoute" :key="item.path">
							<li v-if="item.name != 'dologin' && item.show" :class="['menu-item', `menu-${String(item.name)}`]">
								<a :key="item.path" :href="item.path" class="flex justify-start !p-[1.4rem] !pl-[1.6rem]" :class="getMenuActionStyle(item)" @click.prevent="cutMenuRouter(item.path)">
									<i :class="[`svgtofont-left-${String(item.name)}`]"></i>
									<span class="ml-[1rem]">{{ item.title }}</span>
								</a>
							</li>
						</template>
						<li class="menu-item menu-exit !z-999 !inline-flex relative" @click="doLoginEvent">
							<a href="javascript:;" class="flex justify-start !p-[1.4rem] !pl-[1.6rem]">
								<i class="svgtofont-left-exit"></i>
								<span class="ml-[10px]">退出</span>
							</a>
						</li>
					</ul>
				</BtScrollbar>
			</div>
		</div>
		<!-- 伸缩菜单栏 -->
		<div class="flex-shrink-0">
			<div :class="['menu-toolbar', { 'justify-between': !menuControl.collapse }]">
				<!-- 设置菜单栏伸缩 -->
				<div class="cursor-pointer">
					<BtTooltip class="item" effect="dark" content="点击伸缩菜单栏" placement="top" :enterable="false">
						<div class="setting-hover" @click="cutCollapseViewEvent" @mouseleave="menuCollapseMouseleave" @mouseenter="menuCollapseMouseenter">
							<bt-icon :icon="menuControlIcon ? 'display-menu' : 'shrink-menu'" :color="'#bbb'" />
						</div>
					</BtTooltip>
				</div>

				<!-- 设置菜单显示状态 -->
				<div class="cursor-pointer">
					<BtTooltip v-if="!menuControl.collapse" class="item" effect="dark" content="点击设置菜单显示隐藏" placement="top" :enterable="false">
						<div class="setting-hover" @click="openMenuSettingsEvent">
							<bt-icon icon="menu-setting" :color="'#bbb'" />
						</div>
					</BtTooltip>
				</div>
			</div>
		</div>
	</section>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { useGlobalStore } from '@/store/global'

import { menuRoute } from '@/router/hooks/router-menu'
import { openMessageBoxEvent, copyIpAddressEvent, getMenuActionStyle, doLoginEvent, cutCollapseViewEvent, openMenuSettingsEvent } from './controller'
const { menuControl, panel, serverIp, messageNumberStyle, messageIpNumberLength, menuControlIcon, menuCollapseMouseleave, menuCollapseMouseenter, cutMenuRouter } = useGlobalStore()
const route = useRoute()
</script>

<style lang="scss" scoped>
$icon-size: 100% auto;
$menu-path: '/static/images/menu/';

@mixin bg-icon($icon, $type: 'png') {
	background-image: url('#{$menu-path}#{$icon}.#{$type}');
	background-size: $icon-size;
	background-repeat: no-repeat;
	background-position: center;
}

// 图片ICON
@mixin menu-icon($icon, $type: 'img') {
	&.menu-#{$icon} {
		.icon {
			@include bg-icon($icon, if($type == 'svg', 'svg', 'png'));
		}
		a.is-active .icon {
			@include bg-icon('#{$icon}-active', if($type == 'svg', 'svg', 'png'));
		}
		&:hover .icon {
			@include bg-icon('#{$icon}-hover', if($type == 'svg', 'svg', 'gif'));
		}
	}
}

.sidebar {
	@apply flex flex-col w-full h-full bg-aside text-white;
	box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.05);
}

.sidebar-mask {
	@apply absolute inset-0 bg-black opacity-0 z-99 cursor-disabled;
}

// 消息盒子样式
.layout-message {
	transition: all 0.2s;
	@apply relative items-center justify-start h-[52px] leading-[48px] w-full bg-[#3c444d] hover:bg-primary cursor-pointer transition-all text-white mb-[1px] p-[1.4rem];
	.message-number {
		@apply w-8 h-8 leading-8 bg-[#fc6d26] text-center rounded-[4px] font-bold text-[14px] ml-[2px] absolute right-[8px];
		&.is-min {
			@apply w-6 h-6 leading-6 text-[12px] ml-[4px] absolute right-[0];
		}
	}
	.message-shrink-number {
		@apply w-8 h-8 leading-8 bg-[#fc6d26] text-center rounded-[100%] font-bold text-[14px] absolute top-[8px] right-[6px];
	}
	// END 消息盒子样式
}

// 左侧主菜单样式
.bt-menu {
	transition: width 0.2s;
	@apply bg-[#353d44] border-none;

	&.is-cut-menu {
		.menu-item > a {
			@apply px-[8px] flex justify-center;
			i.menu-icon {
				@apply mr-0;
			}

			span {
				@apply w-0 h-0 text-[0] hidden;
			}
		}
	}
	// 菜单选项样式
	.menu-item {
		@apply flex items-center w-full h-[40px] leading-[40px];
		& + .menu-item {
			@apply mt-[1px];
		}
		a {
			@apply flex items-center w-full h-full text-[#d6d7d9]  pl-[25px] pr-[20px];
			border-left: 4px solid #353d44;
			i {
				@apply text-[#999] text-[16px];
			}
			span {
				@apply text-[14px] font-medium whitespace-nowrap w-[90px] flex  duration-200 ease-in-out;
				transition: width 0.2s;
			}
			&:hover,
			&.is-active {
				background-color: #2c3138 !important;
				border-left-color: #20a53a;
				span,
				i {
					color: #fff;
				}
			}
		}
		.icon {
			@apply block w-[16px] h-[16px] mr-[12px] flex-shrink-0;
		}
	}
}
// END 左侧主菜单样式

// 设置菜单
.setting-hover {
	@apply items-center h-[54px] w-[60px] flex justify-center;
}

.setting-hover:hover {
	@apply bg-[#2c3138];
	i {
		@apply transition-all duration-300;
		color: #ccc !important;
	}
}

.menu-toolbar {
	@apply w-full h-[54px] flex items-center bg-[#3C444D] border-t border-[#404A55];
}
// END 设置菜单
</style>
