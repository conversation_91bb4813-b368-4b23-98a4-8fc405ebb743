<template>
	<el-card class="reset-card-header module-box" body-class="flex h-[52px] items-center p-0">
		<div class="home-header-box flex-1">
			<!-- 宝塔账号 -->
			<el-popover width="328" popper-class="el-popover-shadow">
				<template #reference>
					<div class="mr-[16px] flex items-center">
						<i class="svgtofont-icon-user mr-[4px] !text-[16px]"></i>
						<span class="user-phone">{{ bindUser || '获取中' }}</span>
					</div>
				</template>
				<component :is="UserInfo" />
			</el-popover>
			<!-- 告警 -->
			<div class="mr-[16px] inline-flex items-center" @click="store.jumpRouter('alarm')">
				<i class="svgtofont-alarm-settings !mr-4px !text-[16px]"></i>
				<span class="bt-link">告警</span>
			</div>

			<!-- 需求反馈 -->
			<div class="mr-[16px] inline-flex items-center" @click="npsSurveyV2Dialog({ type: 7, isNoRate: true, isCard: true })">
				<i class="svgtofont-desired !mr-4px !text-[16px]"></i>
				<span class="bt-link">需求反馈</span>
			</div>

			<!-- 系统 -->
			<el-popover width="250" popper-class="el-popover-shadow ">
				<template #reference>
					<span class="cursor-pointer flex items-center">
						<i :class="'mr-4px !text-[16px] svgtofont-icon-' + systemIcon"></i>
						<span class="border-dashed border-b-[1px]"> 系统：{{ systemInfo.name || '获取中' }} </span>
					</span>
				</template>
				<div>
					<span class="!mb-4px inline-block">系统：{{ systemInfo.simpleName || '获取中' }}</span>
					<span class="block"> 持续运行：{{ panelInfo.runningDays || '获取中' }} </span>
				</div>
			</el-popover>
		</div>

		<div class="home-header-box">
			<!-- 待领取优惠券 -->
			<bt-voucher-apply />

			<!-- 授权版本 -->
			<bt-product-state :is-home="true" :disable-pro="true" />

			<!-- 版本号 -->
			<span class="versionInfo mr-[16px] bt-hover-link cursor-pointer" @click="store.histVersionInfoDialog">
				{{ (panelInfo.isBeta ? 'Beta ' : '') + (panelVersion || '获取中') }}
			</span>

			<!-- 找Bug奖宝塔币 -->
			<bt-link class="mr-[16px] reset-link bug-feedback" href="https://www.bt.cn/bbs/forum-39-1.html" v-if="panelInfo.isBeta">[找Bug奖宝塔币]</bt-link>

			<!-- 更新 -->
			<el-badge class="mr-[16px]" is-dot :hidden="panelInfo.upgrade !== 1">
				<span class="bt-link" title="点击获取最新版面板程序" @click="store.updateVersionDialog">更新</span>
			</el-badge>

			<!-- 修复 -->
			<span class="bt-link mr-[16px]" @click="store.onRepair" title="修复面板会获取最新的面板代码程序，包含bug修复">修复</span>

			<!-- 重启 -->
			<span class="bt-link" @click="store.restartSeverDialog" title="点击可选择重启面板或重启服务器">重启</span>
		</div>
	</el-card>
</template>
<script setup lang="ts">
import { npsSurveyV2Dialog } from '@/public/index' // 需求反馈弹窗
import HOME_HEADER_STORE from './store'
import { storeToRefs } from 'pinia'

const UserInfo = defineAsyncComponent(() => import('./user-info/index.vue'))

const store = HOME_HEADER_STORE()
const { bindUser, systemInfo, panelInfo, panelVersion } = storeToRefs(store)

// 系统图标
const systemIcon = computed(() => {
	if (!systemInfo.value.name) return ''
	return systemInfo.value.name.split(' ')[0]?.toLowerCase()
})
</script>

<style lang="css" scoped>
.reset-card-header {
	@apply h-[52px] text-[12px] px-[16px]  mt-0;
}
/* 用户手机号 */
.user-phone {
	@apply border-dashed border-b-[1px] text-primary cursor-pointer;
}
.user-phone:hover {
	color: #1d9534;
}
</style>
