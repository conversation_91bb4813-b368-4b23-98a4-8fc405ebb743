<template>
	<div>
		<el-card v-if="noticeStatus" shadow="always" :body-style="{ padding: '0', display: 'flex', minHeight: '5.2rem' }" class="module-box min-h-[5.2rem]">
			<el-carousel class="playTips" height="5.6rem" direction="vertical">
				<!-- 内存 -->
				<el-carousel-item v-if="memoryStatus" :key="1">
					<i class="svgtofont-el-warning-filled mr-[8px] text-[1.8rem]"></i>
					<span class="mr-[16px]">当前可用物理内存小于64M，这可能导致MySQL自动停止，站点502等错误，请尝试释放内存！</span>
					<bt-link type="info" title="该风险不可忽略，请尽快处理" class="align-middle mr-[16px]" font-size="1.4rem" :disabled="true" href="javascript:;">[ 不可忽略 ]</bt-link>
					<!-- 立即清理 -->
					<bt-link @click="store.onClear" class="align-middle flex items-center" font-size="1.4rem">[ 立即清理 ]</bt-link>
				</el-carousel-item>

				<!-- 用户名 -->
				<el-carousel-item v-if="usernameStatus" :key="2">
					<i class="svgtofont-el-warning-filled mr-[8px] text-[1.8rem]"></i>
					<div class="mr-[16px]">当前面板用户为admin,这可能为面板安全带来风险！</div>
					<bt-link type="info" title="该风险不可忽略，请尽快处理" class="align-middle mr-[16px]" font-size="1.4rem" :disabled="true" href="javascript:;">[ 不可忽略 ]</bt-link>
					<!-- 立即修复 -->
					<bt-link @click="editorUserDialog" class="align-middle flex items-center" font-size="1.4rem">[ 立即修复 ] </bt-link>
				</el-carousel-item>

				<!-- 端口号 -->
				<el-carousel-item v-if="portStatus" :key="3">
					<i class="svgtofont-el-warning-filled mr-[8px] text-[1.8rem]"></i>
					<div class="mr-[16px]">当前面板使用的是默认端口[8888]，可能存在入侵风险，请到面板设置中修改面板端口！</div>
					<!-- 不可忽略 -->
					<bt-link type="info" title="该风险不可忽略，请尽快处理" class="align-middle mr-[16px]" font-size="1.4rem" :disabled="true" href="javascript:;">[ 不可忽略 ]</bt-link>
					<!-- 立即修复 -->
					<bt-link @click="store.editPortInfo" target="_self" class="align-middle flex items-center" font-size="1.4rem">[ 立即修复 ] </bt-link>
				</el-carousel-item>

				<!-- http警告 -->
				<el-carousel-item v-if="httpsStatus" :key="4">
					<i class="svgtofont-el-warning-filled mr-[8px] text-[1.8rem]"></i>
					<div class="mr-[16px]">警告！当前面板使用HTTP访问，可能存在被窃取敏感信息风险，请立即开启【面板SSL】。</div>
					<bt-link type="info" title="该风险不可忽略，请尽快处理" class="align-middle mr-[16px]" :disabled="true" font-size="1.4rem" href="javascript:;">[ 不可忽略 ]</bt-link>
					<!-- 立即清理 -->
					<bt-link @click="store.jumpConfig" class="align-middle flex items-center" font-size="1.4rem">[ 立即处理 ]</bt-link>
				</el-carousel-item>

				<!-- 磁盘占用警告 -->
				<el-carousel-item v-if="diskFullStatus" :key="5">
					<i class="svgtofont-el-warning-filled mr-8px text-[1.8rem]"></i>
					<div class="mr-16px">警告：检测到磁盘可用空间不足，无法访问面板数据库，请前往文件管理清理</div>
					<!-- 跳转文件 -->
					<bt-link @click="store.jumpFiles" class="align-middle">[ 立即前往 ]</bt-link>
				</el-carousel-item>

				<el-carousel-item v-if="inodeFullStatus" :key="6">
					<i class="svgtofont-el-warning-filled mr-8px text-[1.8rem]"></i>
					<div class="mr-16px">
						警告：检测到磁盘【
						<div class="inline-block" :title="inodePath.join('\n')">
							<span v-for="(path, index) in inodePath" :key="index">{{ path }}</span>
						</div>
						】Inode使用率超过90%，可用空间不足，请前往文件管理清理
					</div>
					<!-- 跳转文件 -->
					<span @click="store.jumpFiles" class="align-middle bt-link">[ 立即前往 ]</span>
				</el-carousel-item>
			</el-carousel>
		</el-card>
		<div id="extension"></div>
	</div>
</template>
<script setup lang="ts">
import { editorUserDialog } from '@/public/index'
import HOME_NOTICE_STORE from './store'
import { storeToRefs } from 'pinia'
import { isBoolean } from '@/utils'

const store = HOME_NOTICE_STORE()
const { noticeStatus, memoryStatus, usernameStatus, portStatus, httpsStatus, diskFullStatus, inodeFullStatus, inodePath } = storeToRefs(store)

onMounted(() => {
	store.detectingHttps() // 检测https
	store.checkFullDisk() // 检查满磁盘
	// 专版挂载方法
	nextTick(() => {
		const time = setInterval(async () => {
			if (window.$extension) {
				const plugin = await window.$extension()
				if (isBoolean(plugin.extensionElement) && !plugin.extensionElement) clearTimeout(time)
				if (plugin.extensionElement) {
					plugin.extensionElement()
					clearTimeout(time)
				}
			}
		}, 1000)
	})
})
</script>

<style lang="css" scoped>
.playTips {
	@apply w-full bg-[#f2dede] text-[1.4rem] leading-[3.8rem] text-danger rounded-[6px];
}

.playTips :deep(.el-carousel__item) {
	@apply flex px-[1.6rem] py-1rem;
}

.playTips i {
	@apply leading-[3.8rem];
}

.playTips a {
	font-size: 1.4rem !important;
}

.playTips :deep(.el-carousel__indicators--vertical) {
	@apply mr-[1.6rem];
}

.playTips :deep(.el-carousel__indicator--vertical) {
	@apply inline-block px-[0.4rem] align-middle;
}

.playTips :deep(.el-carousel__indicator--vertical).is-active .el-carousel__button {
	background: red;
}

.playTips :deep(.el-carousel__button) {
	width: 0.6rem;
	height: 0.6rem;
	border: 0.1rem solid red;
	border-radius: 50%;
}

.alert-danger {
	color: #a94442;
	background-color: #f2dede;
	border-color: #ebccd1;
}

.alert {
	padding: 15px;
	margin-bottom: 20px;
	border: 1px solid transparent;
	border-radius: 4px;
}

.tencent_division {
	padding-right: 15px;
	position: relative;
}

.tencent_division::after {
	content: '';
	display: inline-block;
	position: absolute;
	border-right: 1px solid #ececee;
	height: 25px;
	top: 157.5px;
	left: 136px;
}

.tencent_ico {
	display: inline-block;
	height: 25px;
	width: 25px;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAACvElEQVRIS+2VS0iUYRSGn/PPhNLFpPsQhiHhiNFFoUUXKKhFUQuLCslxIgKlzDKidNXMKouImNrkJp2iKwZFoIsgCSkDR1wYNk7BLDKDkG50UZw58c+MGs0/Y7ZwU2d7zvc+3znn/fiEKQiZAgb/GqRSFzAtsg1kNcgKkDAaHcAwmvFJYKKRTzyuqshRRI4CSy3FVBu4bKtIB0oPOaJ+UFdCIIDoE6LRBxh2J7AT1S2xnHAXn7EnFSg1pDpyBpXauIhU4JOGJJFqPYnq2UTNOXxyygpkDTmgs5hBCHQhKnVclvqU46iKeBA5HctnyEzOy9ffa60h1SMlqHEvXiyZXJKhtMs9prlclPCfjasjlIVt5Djfpm3ApjMYMgbIHr5PcUFjSkiTejBYirIE5RXQj1s8v9aPdxLo245oPaqFyYLSyILph8jJ+T6W8+ty4BKw0eICbSgVuKUv7gszOsMO5MdTIBehDdVmxHhNNFoIchAhH2jHbitl5bI3XNVV2LgJmC4DxYtBN8pcoAYoRLmOW2LOjEMCvRdAahB5QVG+ecPx6AzOw+BGzK5R9dLrfIhyCyEPGAZKKZfE/hLH/Po41qGwFZe0xiFdwZ7YmETXUlTwLKn9rtB8IiOHCTpbiXIbWAJ8wWAvZdKSVN+o6zBoR6nHLXWJcb0cRJhDsTP1u2nS9Qh3AAcwiLIbt5g3tg6/KtBGuWwa7eQ5qmtSdnJNN6LcBeYhvEXYRZl0pASMdiL4cYl7FFKL6hnLnTTpZoRmIAsljJ0S9kl32nczuhPTEG7xxCGhUAafI6a7isbcFaWHd441fJjtBTKBIBEq04rbWYxSF3OX0IJLto27a9zGV4AdMaFPWUH6HXko9rTCVknTvgZeXGI+ToufMdC7n4/ZJ+hf9H7S4hBAeGTa9tezE/8nf0H6/ch/yKSG+BPZWO0aiPkdwgAAAABJRU5ErkJggg==);
}

.tencent :deep(.el-card__body) {
	padding: 0;
}
</style>
<style lang="css">
.el-popover.el-popper[x-placement].test-popover {
	@apply mt-0 p-0 bg-white text-[#999];
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-popover.el-popper[x-placement] .popper__arrow {
	@apply border-b-transparent;
}

.el-popover.el-popper[x-placement] .popper__arrow::after {
	@apply border-b-transparent border-t-transparent;
}
</style>
