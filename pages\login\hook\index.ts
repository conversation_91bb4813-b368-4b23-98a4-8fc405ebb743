/* eslint-disable @typescript-eslint/naming-convention */
import Tools from './tools'
import LoginAccount from './loginAccount' // 账号登录
import LoginScan from './loginScan' // 扫码登录

export default class App extends Tools {
	loginType: string

	el: HTMLDivElement

	node: AnyObject

	autoLoginParams: AnyObject

	constructor(parameters: { el: string }) {
		super()
		this.loginType = localStorage.getItem('loginType') || 'account'
		this.el = document.querySelector<HTMLDivElement>(parameters.el) as HTMLDivElement
		this.node = {
			cutTips: null,
			loginAccount: null,
			loginScan: null,
		}
		// 自动登录参数
		this.autoLoginParams = {
			password: '',
			username: '',
		}
		// 模板挂载
		this.render()
	}

	// 模板挂载
	private template() {
		return `<div class="login-bg">
		<div class="login-main">
			<aside class="login-svg" >${Tools.svgLogin}</aside>
			<aside class="cut-login-type ${this.loginType}">
				<div class="tips">
					<span class="account"></span>
					<span class="tips-text">切换扫码登录</span>
				</div>
			</aside>
			<aside class="login-account !hidden"></aside>
			<aside class="login-scan !hidden"></aside>
		</div>`
	}

	// 事件挂载
	private domMount() {
		this.node.cutTips = this.el.querySelector('.tips-text') as HTMLDivElement // 切换提示
		this.node.cutLoginType = this.el.querySelector('.cut-login-type') as HTMLDivElement // 切换提示
		this.node.loginAccount = this.el.querySelector('.login-account') as HTMLDivElement // 账号登录
		this.node.loginScan = this.el.querySelector('.login-scan') as HTMLDivElement
	}

	private eventMount() {
		const { cutLoginType, cutTips } = this.node

		// 切换视图变化
		this.bindEvent(cutLoginType, 'click', () => {
			cutLoginType.classList.add(this.loginType === 'account' ? 'scan' : 'account')
			cutLoginType.classList.remove(this.loginType === 'account' ? 'account' : 'scan')
			if (this.loginType === 'account') {
				this.setLoginScan()
				cutTips.innerText = '切换账号登录'
				localStorage.setItem('loginType', 'scan')
			} else {
				this.setLoginAccount()
				cutTips.innerText = '切换扫码登录'
				localStorage.setItem('loginType', 'account')
			}
		})
	}

	// 设置登录扫描
	private setLoginScan() {
		const { loginScan, loginAccount } = this.node
		this.loginType = 'scan'
		loginScan.classList.remove('!hidden')
		loginAccount.classList.add('!hidden')
		new LoginScan(this.node.loginScan) // 渲染扫码登录
	}

	// 设置账号登录
	private setLoginAccount() {
		const { loginScan, loginAccount } = this.node
		this.loginType = 'account'
		loginScan.classList.add('!hidden')
		loginAccount.classList.remove('!hidden')
		new LoginAccount(this.node.loginAccount) // 渲染账号登录
	}

	// 渲染视图
	private render() {
		this.el.innerHTML = this.template() // 模板挂载
		this.loginType = localStorage.getItem('loginType') || 'account'
		this.domMount()
		this.eventMount()
		if (this.loginType === 'account') {
			this.setLoginAccount()
		} else {
			this.setLoginScan()
		}
	}
}
